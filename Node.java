import java.util.Objects;

/**
 * Represents a node in 3D space for A* pathfinding algorithm.
 * Each node has x, y, z coordinates and associated cost calculations.
 */
public class Node implements Comparable<Node> {
    // Position in 3D space
    private final int x;
    private final int y;
    private final int z;
    
    // A* algorithm costs
    private double gCost; // Cost from start to this node
    private double hCost; // Heuristic cost from this node to end
    private double fCost; // Total cost (g + h)
    
    // Parent node for path reconstruction
    private Node parent;
    
    // Additional properties
    private boolean walkable;
    private double terrainCost; // Additional cost based on terrain type
    private TerrainType terrainType;
    
    public enum TerrainType {
        SOLID(1.0),
        WATER(3.0),
        LAVA(10.0),
        AIR(1.0),
        SAND(1.5),
        SOUL_SAND(2.0),
        ICE(0.8),
        LADDER(1.2),
        VINE(1.3);
        
        private final double movementCost;
        
        TerrainType(double movementCost) {
            this.movementCost = movementCost;
        }
        
        public double getMovementCost() {
            return movementCost;
        }
    }
    
    /**
     * Creates a new node at the specified position.
     */
    public Node(int x, int y, int z) {
        this.x = x;
        this.y = y;
        this.z = z;
        this.walkable = true;
        this.terrainType = TerrainType.AIR;
        this.terrainCost = terrainType.getMovementCost();
        this.gCost = Double.MAX_VALUE;
        this.hCost = 0;
        this.fCost = Double.MAX_VALUE;
    }
    
    /**
     * Creates a new node with specified walkability and terrain type.
     */
    public Node(int x, int y, int z, boolean walkable, TerrainType terrainType) {
        this(x, y, z);
        this.walkable = walkable;
        this.terrainType = terrainType;
        this.terrainCost = terrainType.getMovementCost();
    }
    
    /**
     * Calculates the Euclidean distance to another node in 3D space.
     */
    public double getDistanceTo(Node other) {
        int dx = other.x - this.x;
        int dy = other.y - this.y;
        int dz = other.z - this.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    /**
     * Calculates the Manhattan distance to another node.
     * Often used as a heuristic for grid-based pathfinding.
     */
    public double getManhattanDistanceTo(Node other) {
        return Math.abs(other.x - this.x) + 
               Math.abs(other.y - this.y) + 
               Math.abs(other.z - this.z);
    }
    
    /**
     * Calculates the Chebyshev distance to another node.
     * Useful when diagonal movement has the same cost as cardinal movement.
     */
    public double getChebyshevDistanceTo(Node other) {
        return Math.max(Math.max(
            Math.abs(other.x - this.x),
            Math.abs(other.y - this.y)),
            Math.abs(other.z - this.z));
    }
    
    /**
     * Updates the costs for this node.
     */
    public void updateCosts(double gCost, double hCost) {
        this.gCost = gCost;
        this.hCost = hCost;
        this.fCost = gCost + hCost;
    }
    
    /**
     * Calculates the movement cost to another node.
     * Takes into account terrain costs and vertical movement penalties.
     */
    public double getMovementCostTo(Node other) {
        double distance = getDistanceTo(other);
        double cost = distance * this.terrainCost;
        
        // Add vertical movement penalty
        int verticalDiff = Math.abs(other.y - this.y);
        if (verticalDiff > 0) {
            // Climbing up costs more than moving horizontally
            cost += verticalDiff * 0.5;
        }
        
        return cost;
    }
    
    /**
     * Checks if this node is adjacent to another node.
     * Nodes are adjacent if they differ by at most 1 in each dimension.
     */
    public boolean isAdjacentTo(Node other) {
        return Math.abs(other.x - this.x) <= 1 &&
               Math.abs(other.y - this.y) <= 1 &&
               Math.abs(other.z - this.z) <= 1;
    }
    
    /**
     * Creates a copy of this node with the same position but reset costs.
     */
    public Node copy() {
        return new Node(x, y, z, walkable, terrainType);
    }
    
    @Override
    public int compareTo(Node other) {
        // Compare based on f-cost for priority queue ordering
        return Double.compare(this.fCost, other.fCost);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Node node = (Node) obj;
        return x == node.x && y == node.y && z == node.z;
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(x, y, z);
    }
    
    @Override
    public String toString() {
        return String.format("Node[x=%d, y=%d, z=%d, f=%.2f, g=%.2f, h=%.2f, terrain=%s]",
                x, y, z, fCost, gCost, hCost, terrainType);
    }
    
    // Getters and setters
    public int getX() { return x; }
    public int getY() { return y; }
    public int getZ() { return z; }
    
    public double getGCost() { return gCost; }
    public void setGCost(double gCost) { 
        this.gCost = gCost;
        this.fCost = gCost + hCost;
    }
    
    public double getHCost() { return hCost; }
    public void setHCost(double hCost) { 
        this.hCost = hCost;
        this.fCost = gCost + hCost;
    }
    
    public double getFCost() { return fCost; }
    
    public Node getParent() { return parent; }
    public void setParent(Node parent) { this.parent = parent; }
    
    public boolean isWalkable() { return walkable; }
    public void setWalkable(boolean walkable) { this.walkable = walkable; }
    
    public double getTerrainCost() { return terrainCost; }
    public void setTerrainCost(double terrainCost) { this.terrainCost = terrainCost; }
    
    public TerrainType getTerrainType() { return terrainType; }
    public void setTerrainType(TerrainType terrainType) { 
        this.terrainType = terrainType;
        this.terrainCost = terrainType.getMovementCost();
    }
}
