@echo off
echo ========================================
echo    JasperGoTo - Execucao Direta
echo ========================================
echo.

echo Tentando executar o mod diretamente...
echo (Pulando setup se necessario)
echo.

REM Tentar definir Java 8
for /d %%i in ("C:\Program Files\Java\jdk1.8*") do set JAVA_HOME=%%i
for /d %%i in ("C:\Program Files\Java\jre1.8*") do set JAVA_HOME=%%i
for /d %%i in ("C:\Program Files (x86)\Java\jdk1.8*") do set JAVA_HOME=%%i
for /d %%i in ("C:\Program Files (x86)\Java\jre1.8*") do set JAVA_HOME=%%i

if defined JAVA_HOME (
    echo Usando Java 8: %JAVA_HOME%
    set PATH=%JAVA_HOME%\bin;%PATH%
)

echo Comandos do mod:
echo - /jaspergoto x y z  : <PERSON>ai ate as coordenadas
echo - /jaspergoto stop   : Para o movimento
echo - /jaspergoto clear  : Limpa o caminho
echo.

echo Iniciando cliente...
gradlew runClient

pause