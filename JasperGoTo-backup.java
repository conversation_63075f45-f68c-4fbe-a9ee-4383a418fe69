package com.jaspergoto;

import net.minecraft.client.Minecraft;
import net.minecraft.util.BlockPos;
import net.minecraftforge.client.ClientCommandHandler;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.common.Mod.EventHandler;
import net.minecraftforge.fml.common.event.FMLInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPostInitializationEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

import com.jaspergoto.commands.CommandGoTo;
import com.jaspergoto.pathfinding.PathfindingEngine;
import com.jaspergoto.pathfinding.PathRenderer;
import com.jaspergoto.pathfinding.MovementController;

import java.util.List;

@Mod(modid = JasperGoTo.MODID, version = JasperGoTo.VERSION, name = JasperGoTo.NAME)
public class JasperGoTo
{
    public static final String MODID = "jaspergoto";
    public static final String VERSION = "1.0.0";
    public static final String NAME = "JasperGoTo - Pathfinding";
    
    // Instância singleton
    @Mod.Instance(MODID)
    public static JasperGoTo instance;
    
    // Sistema de pathfinding
    private static PathfindingEngine pathfindingEngine;
    private static PathRenderer pathRenderer;
    private static MovementController movementController;
    
    @EventHandler
    public void preInit(FMLPreInitializationEvent event)
    {
        System.out.println("[JasperGoTo] Pre-initialization - Pathfinding System");
    }
    
    @EventHandler
    public void init(FMLInitializationEvent event)
    {
        System.out.println("[JasperGoTo] Initializing Pathfinding System...");
        
        if (event.getSide() == Side.CLIENT) {
            // Registrar comando
            ClientCommandHandler.instance.registerCommand(new CommandGoTo());
            
            // Registrar este mod para eventos
            MinecraftForge.EVENT_BUS.register(this);
        }
    }
    
    @EventHandler
    public void postInit(FMLPostInitializationEvent event)
    {
        System.out.println("[JasperGoTo] Pathfinding system ready!");
        System.out.println("[JasperGoTo] Use /jaspergoto <x> <y> <z> para navegar automaticamente!");
    }
    
    @SideOnly(Side.CLIENT)
    @SubscribeEvent
    public void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.START) return;
        if (Minecraft.getMinecraft().theWorld == null) return;
        if (Minecraft.getMinecraft().thePlayer == null) return;
        
        // Inicializar sistemas se necessário
        if (pathfindingEngine == null) {
            pathfindingEngine = new PathfindingEngine(Minecraft.getMinecraft().theWorld);
            pathRenderer = new PathRenderer(pathfindingEngine);
            movementController = new MovementController(pathfindingEngine);
            
            MinecraftForge.EVENT_BUS.register(pathRenderer);
        }
        
        // Atualizar movimento
        if (movementController != null) {
            movementController.update();
        }
    }
    
    // Métodos estáticos para os comandos
    public static boolean startPathfinding(BlockPos start, BlockPos target) {
        if (pathfindingEngine == null) return false;
        
        List<BlockPos> path = pathfindingEngine.findPath(start, target);
        if (path != null && !path.isEmpty()) {
            if (movementController != null) {
                movementController.reset();
            }
            return true;
        }
        return false;
    }
    
    public static void stopPathfinding() {
        if (movementController != null) {
            movementController.stopMovement();
        }
        if (pathfindingEngine != null) {
            pathfindingEngine.clearPath();
        }
    }
    
    public static void clearPath() {
        if (pathfindingEngine != null) {
            pathfindingEngine.clearPath();
        }
        if (movementController != null) {
            movementController.reset();
        }
    }
}
