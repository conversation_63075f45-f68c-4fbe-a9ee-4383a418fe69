@echo off
echo ========================================
echo    Compilacao Corrigida do JasperGoTo
echo ========================================
echo.

REM Definir Java 8 JDK explicitamente
set "JAVA_HOME=C:\Program Files\Java\jdk1.8.0_333"
set "PATH=%JAVA_HOME%\bin;%PATH%"

if exist "%JAVA_HOME%\bin\javac.exe" (
    echo Usando Java 8 JDK: %JAVA_HOME%
    java -version
) else (
    echo ERRO: Java 8 JDK nao encontrado em %JAVA_HOME%
    echo Por favor, instale o Java 8 Development Kit (JDK)
    echo Download em: https://www.oracle.com/java/technologies/javase/javase-jdk8-downloads.html
    echo.
    pause
    exit /b 1
)

echo.
echo Configurando ambiente para Gradle...
set "GRADLE_OPTS=-Dorg.gradle.java.home=%JAVA_HOME%"

echo.
echo Limpando builds anteriores...
if exist "build" rmdir /s /q build

echo.
echo Compilando com Gradle usando Java 8...
echo Executando: .\gradlew build --no-daemon
.\gradlew build --no-daemon

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo    SUCESSO! Mod compilado corretamente
    echo ========================================
    echo.
    echo Arquivos gerados:
    dir build\libs\*.jar
    echo.
    echo Para usar o mod:
    echo 1. Copie o arquivo .jar da pasta build/libs para a pasta .minecraft/mods
    echo 2. Execute Minecraft 1.8.9 com Forge
    echo.
) else (
    echo.
    echo ========================================
    echo    FALHA NA COMPILACAO
    echo ========================================
    echo.
    echo Problemas comuns e solucoes:
    echo 1. Certifique-se de que o Java 8 JDK esta instalado corretamente
    echo 2. Verifique se ha erros no codigo fonte
    echo 3. Tente executar .\gradlew clean antes de compilar
    echo.
)

pause