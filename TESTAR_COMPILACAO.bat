@echo off
echo ========================================
echo    Teste de Compilacao do JasperGoTo
echo ========================================
echo.

echo Limpando builds anteriores...
if exist "build" rmdir /s /q build

echo.
echo Compilando com Gradle usando Java 8...
.\gradlew build --no-daemon --stacktrace

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo    SUCESSO! Mod compilado corretamente
    echo ========================================
    echo.
    echo Arquivos gerados:
    dir build\libs\*.jar
    echo.
) else (
    echo.
    echo ========================================
    echo    FALHA NA COMPILACAO
    echo ========================================
    echo.
    echo Verifique as instrucoes em INSTRUCOES_COMPILAR.md
    echo.
)

pause