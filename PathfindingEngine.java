import java.util.*;
import java.util.concurrent.*;

/**
 * Main pathfinding engine implementing the A* algorithm with 3D support.
 * Handles path calculation, node expansion, and heuristic evaluation.
 */
public class PathfindingEngine {
    
    // Configuration parameters
    private final int maxSearchNodes;
    private final double maxSearchTime; // in seconds
    private final HeuristicType heuristicType;
    private final boolean allowDiagonalMovement;
    private final boolean allowVerticalMovement;
    private final int maxJumpHeight;
    private final int maxFallDistance;
    
    // Components
    private final ObstacleDetector obstacleDetector;
    private final ObstacleDetector.WorldAccessor world;
    
    // Statistics
    private long nodesExplored;
    private long totalPathsCalculated;
    private double averagePathLength;
    private double averageCalculationTime;
    
    public enum HeuristicType {
        EUCLIDEAN,
        MANHATTAN,
        CHEBYSHEV,
        OCTILE
    }
    
    /**
     * Result of a pathfinding operation.
     */
    public static class PathResult {
        private final List<Node> path;
        private final boolean success;
        private final double totalCost;
        private final long nodesExplored;
        private final long calculationTimeMs;
        private final String failureReason;
        
        private PathResult(List<Node> path, boolean success, double totalCost,
                          long nodesExplored, long calculationTimeMs, String failureReason) {
            this.path = path != null ? new ArrayList<>(path) : new ArrayList<>();
            this.success = success;
            this.totalCost = totalCost;
            this.nodesExplored = nodesExplored;
            this.calculationTimeMs = calculationTimeMs;
            this.failureReason = failureReason;
        }
        
        public static PathResult success(List<Node> path, double totalCost,
                                        long nodesExplored, long calculationTimeMs) {
            return new PathResult(path, true, totalCost, nodesExplored, calculationTimeMs, null);
        }
        
        public static PathResult failure(String reason, long nodesExplored, long calculationTimeMs) {
            return new PathResult(null, false, 0, nodesExplored, calculationTimeMs, reason);
        }
        
        // Getters
        public List<Node> getPath() { return new ArrayList<>(path); }
        public boolean isSuccess() { return success; }
        public double getTotalCost() { return totalCost; }
        public long getNodesExplored() { return nodesExplored; }
        public long getCalculationTimeMs() { return calculationTimeMs; }
        public String getFailureReason() { return failureReason; }
    }
    
    /**
     * Creates a pathfinding engine with specified configuration.
     */
    public PathfindingEngine(ObstacleDetector.WorldAccessor world,
                            int maxSearchNodes,
                            double maxSearchTime,
                            HeuristicType heuristicType,
                            boolean allowDiagonalMovement,
                            boolean allowVerticalMovement,
                            int maxJumpHeight,
                            int maxFallDistance) {
        this.world = world;
        this.maxSearchNodes = maxSearchNodes;
        this.maxSearchTime = maxSearchTime;
        this.heuristicType = heuristicType;
        this.allowDiagonalMovement = allowDiagonalMovement;
        this.allowVerticalMovement = allowVerticalMovement;
        this.maxJumpHeight = maxJumpHeight;
        this.maxFallDistance = maxFallDistance;
        this.obstacleDetector = new ObstacleDetector(world, maxFallDistance, false, true);
        this.nodesExplored = 0;
        this.totalPathsCalculated = 0;
        this.averagePathLength = 0;
        this.averageCalculationTime = 0;
    }
    
    /**
     * Creates a pathfinding engine with default configuration.
     */
    public PathfindingEngine(ObstacleDetector.WorldAccessor world) {
        this(world, 10000, 5.0, HeuristicType.OCTILE, true, true, 1, 3);
    }
    
    /**
     * Finds a path from start to goal using A* algorithm.
     */
    public PathResult findPath(Node start, Node goal) {
        long startTime = System.currentTimeMillis();
        
        // Validation
        if (!isValidPosition(start) || !isValidPosition(goal)) {
            return PathResult.failure("Invalid start or goal position", 0, 0);
        }
        
        if (!obstacleDetector.isWalkable(goal.getX(), goal.getY(), goal.getZ())) {
            return PathResult.failure("Goal position is not walkable", 0, 0);
        }
        
        // Initialize data structures
        PriorityQueue<Node> openSet = new PriorityQueue<>();
        Set<Node> closedSet = new HashSet<>();
        Map<Node, Node> cameFrom = new HashMap<>();
        Map<Node, Double> gScore = new HashMap<>();
        
        // Initialize start node
        start.setGCost(0);
        start.setHCost(calculateHeuristic(start, goal));
        gScore.put(start, 0.0);
        openSet.add(start);
        
        long nodesProcessed = 0;
        
        // A* main loop
        while (!openSet.isEmpty()) {
            // Check time limit
            if ((System.currentTimeMillis() - startTime) / 1000.0 > maxSearchTime) {
                return PathResult.failure("Search timeout", nodesProcessed,
                                        System.currentTimeMillis() - startTime);
            }
            
            // Check node limit
            if (nodesProcessed >= maxSearchNodes) {
                return PathResult.failure("Node limit reached", nodesProcessed,
                                        System.currentTimeMillis() - startTime);
            }
            
            Node current = openSet.poll();
            nodesProcessed++;
            
            // Check if we reached the goal
            if (current.equals(goal)) {
                List<Node> path = reconstructPath(cameFrom, current);
                double totalCost = gScore.get(current);
                long calcTime = System.currentTimeMillis() - startTime;
                
                // Update statistics
                updateStatistics(path.size(), calcTime);
                
                return PathResult.success(path, totalCost, nodesProcessed, calcTime);
            }
            
            closedSet.add(current);
            
            // Explore neighbors
            List<Node> neighbors = getNeighbors(current);
            for (Node neighbor : neighbors) {
                if (closedSet.contains(neighbor)) {
                    continue;
                }
                
                // Check if path to neighbor is clear
                if (!obstacleDetector.isPathClear(current, neighbor)) {
                    continue;
                }
                
                // Calculate tentative g score
                double tentativeGScore = gScore.get(current) + 
                                        calculateMovementCost(current, neighbor);
                
                if (tentativeGScore < gScore.getOrDefault(neighbor, Double.MAX_VALUE)) {
                    // This path to neighbor is better
                    cameFrom.put(neighbor, current);
                    gScore.put(neighbor, tentativeGScore);
                    neighbor.setGCost(tentativeGScore);
                    neighbor.setHCost(calculateHeuristic(neighbor, goal));
                    neighbor.setParent(current);
                    
                    if (!openSet.contains(neighbor)) {
                        openSet.add(neighbor);
                    }
                }
            }
        }
        
        return PathResult.failure("No path found", nodesProcessed,
                                System.currentTimeMillis() - startTime);
    }
    
    /**
     * Finds a path asynchronously.
     */
    public CompletableFuture<PathResult> findPathAsync(Node start, Node goal) {
        return CompletableFuture.supplyAsync(() -> findPath(start, goal));
    }
    
    /**
     * Finds paths to multiple goals and returns the best one.
     */
    public PathResult findBestPath(Node start, List<Node> goals) {
        PathResult bestResult = null;
        double bestCost = Double.MAX_VALUE;
        
        for (Node goal : goals) {
            PathResult result = findPath(start, goal);
            if (result.isSuccess() && result.getTotalCost() < bestCost) {
                bestResult = result;
                bestCost = result.getTotalCost();
            }
        }
        
        if (bestResult == null) {
            return PathResult.failure("No path to any goal", 0, 0);
        }
        
        return bestResult;
    }
    
    /**
     * Gets all valid neighbors of a node.
     */
    private List<Node> getNeighbors(Node node) {
        List<Node> neighbors = new ArrayList<>();
        int x = node.getX();
        int y = node.getY();
        int z = node.getZ();
        
        // Cardinal directions (6 directions in 3D)
        neighbors.add(new Node(x + 1, y, z));
        neighbors.add(new Node(x - 1, y, z));
        neighbors.add(new Node(x, y, z + 1));
        neighbors.add(new Node(x, y, z - 1));
        
        // Vertical movement
        if (allowVerticalMovement) {
            // Up movement (jumping/climbing)
            for (int jumpHeight = 1; jumpHeight <= maxJumpHeight; jumpHeight++) {
                neighbors.add(new Node(x, y + jumpHeight, z));
            }
            
            // Down movement (falling)
            for (int fallHeight = 1; fallHeight <= maxFallDistance; fallHeight++) {
                neighbors.add(new Node(x, y - fallHeight, z));
            }
        }
        
        // Diagonal movement on same level
        if (allowDiagonalMovement) {
            neighbors.add(new Node(x + 1, y, z + 1));
            neighbors.add(new Node(x + 1, y, z - 1));
            neighbors.add(new Node(x - 1, y, z + 1));
            neighbors.add(new Node(x - 1, y, z - 1));
        }
        
        // Diagonal movement with vertical component
        if (allowDiagonalMovement && allowVerticalMovement) {
            // Diagonal jumps
            for (int dy = -1; dy <= maxJumpHeight; dy++) {
                if (dy == 0) continue;
                neighbors.add(new Node(x + 1, y + dy, z));
                neighbors.add(new Node(x - 1, y + dy, z));
                neighbors.add(new Node(x, y + dy, z + 1));
                neighbors.add(new Node(x, y + dy, z - 1));
                
                if (allowDiagonalMovement) {
                    neighbors.add(new Node(x + 1, y + dy, z + 1));
                    neighbors.add(new Node(x + 1, y + dy, z - 1));
                    neighbors.add(new Node(x - 1, y + dy, z + 1));
                    neighbors.add(new Node(x - 1, y + dy, z - 1));
                }
            }
        }
        
        // Filter out invalid positions
        neighbors.removeIf(n -> !isValidPosition(n));
        
        // Set terrain types for neighbors
        for (Node neighbor : neighbors) {
            ObstacleDetector.BlockType blockType = 
                world.getBlockAt(neighbor.getX(), neighbor.getY(), neighbor.getZ());
            neighbor.setTerrainType(mapBlockTypeToTerrainType(blockType));
        }
        
        return neighbors;
    }
    
    /**
     * Calculates the heuristic cost from current to goal.
     */
    private double calculateHeuristic(Node current, Node goal) {
        switch (heuristicType) {
            case EUCLIDEAN:
                return current.getDistanceTo(goal);
            case MANHATTAN:
                return current.getManhattanDistanceTo(goal);
            case CHEBYSHEV:
                return current.getChebyshevDistanceTo(goal);
            case OCTILE:
                return calculateOctileDistance(current, goal);
            default:
                return current.getDistanceTo(goal);
        }
    }
    
    /**
     * Calculates octile distance (good for 8-directional movement).
     */
    private double calculateOctileDistance(Node current, Node goal) {
        int dx = Math.abs(goal.getX() - current.getX());
        int dy = Math.abs(goal.getY() - current.getY());
        int dz = Math.abs(goal.getZ() - current.getZ());
        
        int dmin = Math.min(Math.min(dx, dy), dz);
        int dmax = Math.max(Math.max(dx, dy), dz);
        int dmid = dx + dy + dz - dmin - dmax;
        
        return dmax + (Math.sqrt(2) - 1) * dmid + (Math.sqrt(3) - Math.sqrt(2)) * dmin;
    }
    
    /**
     * Calculates the movement cost between two nodes.
     */
    private double calculateMovementCost(Node from, Node to) {
        double baseCost = from.getMovementCostTo(to);
        double obstaclePenalty = obstacleDetector.getMovementPenalty(
            to.getX(), to.getY(), to.getZ());
        
        // Add movement type penalty
        ObstacleDetector.MovementType movementType = 
            obstacleDetector.getRequiredMovement(from, to);
        double movementPenalty = getMovementTypePenalty(movementType);
        
        return baseCost * obstaclePenalty * movementPenalty;
    }
    
    /**
     * Gets penalty multiplier for different movement types.
     */
    private double getMovementTypePenalty(ObstacleDetector.MovementType type) {
        switch (type) {
            case WALK:
                return 1.0;
            case JUMP:
                return 1.5;
            case FALL:
                return 0.8;
            case SWIM:
                return 2.0;
            case CLIMB:
                return 1.8;
            default:
                return 1.0;
        }
    }
    
    /**
     * Reconstructs the path from start to goal.
     */
    private List<Node> reconstructPath(Map<Node, Node> cameFrom, Node current) {
        List<Node> path = new ArrayList<>();
        path.add(current);
        
        while (cameFrom.containsKey(current)) {
            current = cameFrom.get(current);
            path.add(0, current);
        }
        
        return path;
    }
    
    /**
     * Checks if a position is valid.
     */
    private boolean isValidPosition(Node node) {
        return world.isLoaded(node.getX(), node.getY(), node.getZ()) &&
               node.getY() >= 0 && node.getY() < 256;
    }
    
    /**
     * Maps block type to terrain type for node.
     */
    private Node.TerrainType mapBlockTypeToTerrainType(ObstacleDetector.BlockType blockType) {
        switch (blockType) {
            case WATER:
                return Node.TerrainType.WATER;
            case LAVA:
                return Node.TerrainType.LAVA;
            case SAND:
                return Node.TerrainType.SAND;
            case SOUL_SAND:
                return Node.TerrainType.SOUL_SAND;
            case ICE:
            case PACKED_ICE:
                return Node.TerrainType.ICE;
            case LADDER:
                return Node.TerrainType.LADDER;
            case VINE:
                return Node.TerrainType.VINE;
            case AIR:
                return Node.TerrainType.AIR;
            default:
                return Node.TerrainType.SOLID;
        }
    }
    
    /**
     * Updates pathfinding statistics.
     */
    private void updateStatistics(int pathLength, long calculationTime) {
        totalPathsCalculated++;
        averagePathLength = (averagePathLength * (totalPathsCalculated - 1) + pathLength) 
                           / totalPathsCalculated;
        averageCalculationTime = (averageCalculationTime * (totalPathsCalculated - 1) + calculationTime) 
                                / totalPathsCalculated;
    }
    
    /**
     * Resets all statistics.
     */
    public void resetStatistics() {
        nodesExplored = 0;
        totalPathsCalculated = 0;
        averagePathLength = 0;
        averageCalculationTime = 0;
    }
    
    // Getters for statistics
    public long getNodesExplored() { return nodesExplored; }
    public long getTotalPathsCalculated() { return totalPathsCalculated; }
    public double getAveragePathLength() { return averagePathLength; }
    public double getAverageCalculationTime() { return averageCalculationTime; }
    
    // Configuration getters
    public int getMaxSearchNodes() { return maxSearchNodes; }
    public double getMaxSearchTime() { return maxSearchTime; }
    public HeuristicType getHeuristicType() { return heuristicType; }
    public boolean isAllowDiagonalMovement() { return allowDiagonalMovement; }
    public boolean isAllowVerticalMovement() { return allowVerticalMovement; }
    public int getMaxJumpHeight() { return maxJumpHeight; }
    public int getMaxFallDistance() { return maxFallDistance; }
}
