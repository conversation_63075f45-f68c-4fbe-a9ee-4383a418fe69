@echo off
echo ========================================
echo   JasperGoTo - Compilar e Executar
echo ========================================
echo.

REM Configurar Java 8
set "JAVA_HOME=C:\Program Files\Java\jdk1.8.0_333"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo Compilando mod...
set "JAVA_HOME=C:\Program Files\Java\jdk1.8.0_333"
set "PATH=%JAVA_HOME%\bin;%PATH%"
call gradlew.bat clean build

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Erro na compilacao!
    pause
    exit /b 1
)

echo ✅ Compilacao concluida!
echo.

echo Copiando mod para pasta run/mods...
if not exist "run\mods" mkdir "run\mods"
powershell -Command "Copy-Item 'build\libs\jaspergoto-1.0.0.jar' 'run\mods\' -Force" >nul

echo ✅ Mod copiado!
echo.

echo ========================================
echo        INICIANDO MINECRAFT
echo ========================================
echo.
echo Comandos do mod:
echo   /jaspergoto ^<x^> ^<y^> ^<z^>  - Navegar
echo   /jaspergoto stop           - Parar
echo   /jaspergoto clear          - Limpar
echo.

echo Executando cliente...
set "JAVA_HOME=C:\Program Files\Java\jdk1.8.0_333"
set "PATH=%JAVA_HOME%\bin;%PATH%"
echo Pressione qualquer tecla para iniciar o Minecraft...
pause >nul
call gradlew.bat runClient

echo.
echo Cliente encerrado.
pause
