@echo off
echo ========================================
echo   JasperGoTo - Compilacao e Execucao
echo ========================================
echo.

REM Tentar encontrar Java 8
set JAVA8_PATH=
for /f "tokens=*" %%i in ('where java 2^>nul') do (
    for /f "tokens=*" %%j in ('%%i -version 2^>^&1 ^| findstr "1.8"') do (
        set JAVA8_PATH=%%i
        goto :found_java8
    )
)

:found_java8
if "%JAVA8_PATH%"=="" (
    echo ERRO: Java 8 nao encontrado!
    echo Por favor instale Java 8 para continuar.
    pause
    exit /b 1
)

echo Java 8 encontrado: %JAVA8_PATH%
echo.

REM Definir JAVA_HOME temporariamente
for %%i in ("%JAVA8_PATH%") do set "JAVA_HOME=%%~dpi.."
echo Usando JAVA_HOME: %JAVA_HOME%

REM Limpar build anterior
echo Limpando build anterior...
if exist build rmdir /s /q build

REM Compilar
echo Compilando o mod...
gradlew.bat clean build --no-daemon

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo    COMPILACAO CONCLUIDA COM SUCESSO!
    echo ========================================
    echo.
    
    REM Mostrar JAR criado
    if exist "build\libs\*.jar" (
        echo JAR criado:
        dir build\libs\*.jar
        echo.
    )
    
    REM Perguntar se quer executar o cliente
    set /p run_client="Deseja executar o cliente de teste? (s/n): "
    if /i "%run_client%"=="s" (
        echo.
        echo Iniciando cliente de desenvolvimento...
        gradlew.bat runClient
    )
) else (
    echo.
    echo ========================================
    echo    FALHA NA COMPILACAO
    echo ========================================
    echo.
    echo Verifique os erros acima.
)

echo.
pause
