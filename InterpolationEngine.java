import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.logging.Logger;

/**
 * Provides smooth movement interpolation between positions for natural movement.
 * Implements various interpolation algorithms including linear, cubic, and bezier curves.
 */
public class InterpolationEngine {
    private static final Logger LOGGER = Logger.getLogger(InterpolationEngine.class.getName());
    
    /**
     * Interpolation types available
     */
    public enum InterpolationType {
        LINEAR("Linear interpolation"),
        CUBIC("Cubic interpolation"),
        EASE_IN("Ease in (slow start)"),
        EASE_OUT("Ease out (slow end)"),
        EASE_IN_OUT("Ease in-out (slow start and end)"),
        BEZIER("Bezier curve interpolation"),
        CATMULL_ROM("Catmull-Rom spline"),
        HERMITE("Hermite spline"),
        SINE("Sine wave interpolation"),
        EXPONENTIAL("Exponential interpolation");
        
        private final String description;
        
        InterpolationType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * Waypoint for path interpolation
     */
    public static class Waypoint {
        public final MovementController.Position position;
        public final double timestamp;
        public final float speed;
        
        public Waypoint(MovementController.Position position, double timestamp, float speed) {
            this.position = position;
            this.timestamp = timestamp;
            this.speed = speed;
        }
    }
    
    /**
     * Path for complex interpolation
     */
    public static class InterpolatedPath {
        private final List<Waypoint> waypoints;
        private final InterpolationType type;
        private int currentSegment;
        
        public InterpolatedPath(InterpolationType type) {
            this.waypoints = new ArrayList<>();
            this.type = type;
            this.currentSegment = 0;
        }
        
        public void addWaypoint(Waypoint waypoint) {
            waypoints.add(waypoint);
        }
        
        public List<Waypoint> getWaypoints() {
            return waypoints;
        }
        
        public boolean isComplete() {
            return currentSegment >= waypoints.size() - 1;
        }
    }
    
    // Configuration
    private InterpolationType defaultType;
    private float smoothingFactor;
    private boolean adaptiveSmoothing;
    
    // Path history for smooth transitions
    private final ConcurrentLinkedQueue<MovementController.Position> positionHistory;
    private static final int MAX_HISTORY_SIZE = 10;
    
    /**
     * Constructor initializes the interpolation engine
     */
    public InterpolationEngine() {
        this.defaultType = InterpolationType.EASE_IN_OUT;
        this.smoothingFactor = 0.3f;
        this.adaptiveSmoothing = true;
        this.positionHistory = new ConcurrentLinkedQueue<>();
        
        LOGGER.info("Interpolation engine initialized with default type: " + defaultType);
    }
    
    /**
     * Interpolate between two positions
     */
    public MovementController.Position interpolate(MovementController.Position from, 
                                                  MovementController.Position to, 
                                                  double t) {
        return interpolate(from, to, t, defaultType);
    }
    
    /**
     * Interpolate between two positions with specific type
     */
    public MovementController.Position interpolate(MovementController.Position from, 
                                                  MovementController.Position to, 
                                                  double t, 
                                                  InterpolationType type) {
        // Clamp t to [0, 1]
        t = Math.max(0.0, Math.min(1.0, t));
        
        // Apply interpolation based on type
        double adjustedT;
        switch (type) {
            case LINEAR:
                adjustedT = t;
                break;
            case CUBIC:
                adjustedT = cubicInterpolation(t);
                break;
            case EASE_IN:
                adjustedT = easeIn(t);
                break;
            case EASE_OUT:
                adjustedT = easeOut(t);
                break;
            case EASE_IN_OUT:
                adjustedT = easeInOut(t);
                break;
            case SINE:
                adjustedT = sineInterpolation(t);
                break;
            case EXPONENTIAL:
                adjustedT = exponentialInterpolation(t);
                break;
            default:
                adjustedT = easeInOut(t);
                break;
        }
        
        // Interpolate position components
        double x = lerp(from.x, to.x, adjustedT);
        double y = lerp(from.y, to.y, adjustedT);
        double z = lerp(from.z, to.z, adjustedT);
        
        MovementController.Position interpolated = new MovementController.Position(x, y, z);
        
        // Add to history for smoothing
        updatePositionHistory(interpolated);
        
        // Apply adaptive smoothing if enabled
        if (adaptiveSmoothing) {
            interpolated = applySmoothingFilter(interpolated);
        }
        
        return interpolated;
    }
    
    /**
     * Interpolate along a path with multiple waypoints
     */
    public MovementController.Position interpolatePath(InterpolatedPath path, double t) {
        if (path.waypoints.size() < 2) {
            return path.waypoints.isEmpty() ? new MovementController.Position(0, 0, 0) : 
                   path.waypoints.get(0).position;
        }
        
        // Find the current segment
        int numSegments = path.waypoints.size() - 1;
        double segmentLength = 1.0 / numSegments;
        int segment = (int)(t / segmentLength);
        segment = Math.min(segment, numSegments - 1);
        
        // Calculate local t within the segment
        double localT = (t - segment * segmentLength) / segmentLength;
        
        // Get waypoints for current segment
        Waypoint from = path.waypoints.get(segment);
        Waypoint to = path.waypoints.get(segment + 1);
        
        // Interpolate between waypoints
        return interpolate(from.position, to.position, localT, path.type);
    }
    
    /**
     * Create a Bezier curve interpolation
     */
    public MovementController.Position bezierInterpolate(MovementController.Position p0,
                                                        MovementController.Position p1,
                                                        MovementController.Position p2,
                                                        MovementController.Position p3,
                                                        double t) {
        double u = 1 - t;
        double tt = t * t;
        double uu = u * u;
        double uuu = uu * u;
        double ttt = tt * t;
        
        // Cubic Bezier formula
        double x = uuu * p0.x + 3 * uu * t * p1.x + 3 * u * tt * p2.x + ttt * p3.x;
        double y = uuu * p0.y + 3 * uu * t * p1.y + 3 * u * tt * p2.y + ttt * p3.y;
        double z = uuu * p0.z + 3 * uu * t * p1.z + 3 * u * tt * p2.z + ttt * p3.z;
        
        return new MovementController.Position(x, y, z);
    }
    
    /**
     * Catmull-Rom spline interpolation for smooth paths
     */
    public MovementController.Position catmullRomInterpolate(MovementController.Position p0,
                                                            MovementController.Position p1,
                                                            MovementController.Position p2,
                                                            MovementController.Position p3,
                                                            double t) {
        double tt = t * t;
        double ttt = tt * t;
        
        // Catmull-Rom coefficients
        double a = -0.5 * ttt + tt - 0.5 * t;
        double b = 1.5 * ttt - 2.5 * tt + 1.0;
        double c = -1.5 * ttt + 2.0 * tt + 0.5 * t;
        double d = 0.5 * ttt - 0.5 * tt;
        
        double x = a * p0.x + b * p1.x + c * p2.x + d * p3.x;
        double y = a * p0.y + b * p1.y + c * p2.y + d * p3.y;
        double z = a * p0.z + b * p1.z + c * p2.z + d * p3.z;
        
        return new MovementController.Position(x, y, z);
    }
    
    /**
     * Hermite spline interpolation
     */
    public MovementController.Position hermiteInterpolate(MovementController.Position p0,
                                                         MovementController.Position p1,
                                                         MovementController.Position tangent0,
                                                         MovementController.Position tangent1,
                                                         double t) {
        double tt = t * t;
        double ttt = tt * t;
        
        // Hermite basis functions
        double h00 = 2 * ttt - 3 * tt + 1;
        double h10 = ttt - 2 * tt + t;
        double h01 = -2 * ttt + 3 * tt;
        double h11 = ttt - tt;
        
        double x = h00 * p0.x + h10 * tangent0.x + h01 * p1.x + h11 * tangent1.x;
        double y = h00 * p0.y + h10 * tangent0.y + h01 * p1.y + h11 * tangent1.y;
        double z = h00 * p0.z + h10 * tangent0.z + h01 * p1.z + h11 * tangent1.z;
        
        return new MovementController.Position(x, y, z);
    }
    
    /**
     * Update position history for smoothing
     */
    private void updatePositionHistory(MovementController.Position position) {
        positionHistory.offer(position);
        while (positionHistory.size() > MAX_HISTORY_SIZE) {
            positionHistory.poll();
        }
    }
    
    /**
     * Apply smoothing filter based on position history
     */
    private MovementController.Position applySmoothingFilter(MovementController.Position current) {
        if (positionHistory.size() < 3) {
            return current;
        }
        
        // Apply weighted average smoothing
        double totalWeight = 0;
        double x = 0, y = 0, z = 0;
        int index = 0;
        
        for (MovementController.Position pos : positionHistory) {
            double weight = Math.pow(smoothingFactor, positionHistory.size() - index - 1);
            x += pos.x * weight;
            y += pos.y * weight;
            z += pos.z * weight;
            totalWeight += weight;
            index++;
        }
        
        // Add current position with highest weight
        double currentWeight = 1.0;
        x += current.x * currentWeight;
        y += current.y * currentWeight;
        z += current.z * currentWeight;
        totalWeight += currentWeight;
        
        // Normalize
        x /= totalWeight;
        y /= totalWeight;
        z /= totalWeight;
        
        return new MovementController.Position(x, y, z);
    }
    
    // Interpolation functions
    
    private double lerp(double a, double b, double t) {
        return a + (b - a) * t;
    }
    
    private double cubicInterpolation(double t) {
        return t * t * (3.0 - 2.0 * t);
    }
    
    private double easeIn(double t) {
        return t * t * t;
    }
    
    private double easeOut(double t) {
        return 1.0 - Math.pow(1.0 - t, 3);
    }
    
    private double easeInOut(double t) {
        if (t < 0.5) {
            return 4.0 * t * t * t;
        } else {
            double p = 2.0 * t - 2.0;
            return 1.0 + p * p * p / 2.0;
        }
    }
    
    private double sineInterpolation(double t) {
        return 0.5 - 0.5 * Math.cos(t * Math.PI);
    }
    
    private double exponentialInterpolation(double t) {
        if (t == 0) return 0;
        if (t == 1) return 1;
        return Math.pow(2, 10 * (t - 1));
    }
    
    /**
     * Calculate smooth velocity for movement
     */
    public MovementController.Velocity calculateSmoothVelocity(MovementController.Position from,
                                                              MovementController.Position to,
                                                              double duration) {
        double dx = (to.x - from.x) / duration;
        double dy = (to.y - from.y) / duration;
        double dz = (to.z - from.z) / duration;
        
        // Apply smoothing to velocity
        if (adaptiveSmoothing) {
            double speed = Math.sqrt(dx * dx + dy * dy + dz * dz);
            if (speed > 0) {
                // Smooth acceleration/deceleration
                double smoothedSpeed = speed * easeInOut(0.5);
                double ratio = smoothedSpeed / speed;
                dx *= ratio;
                dy *= ratio;
                dz *= ratio;
            }
        }
        
        return new MovementController.Velocity(dx, dy, dz);
    }
    
    /**
     * Create a smooth path between multiple points
     */
    public InterpolatedPath createSmoothPath(List<MovementController.Position> points, 
                                            InterpolationType type) {
        InterpolatedPath path = new InterpolatedPath(type);
        
        double totalDistance = 0;
        for (int i = 1; i < points.size(); i++) {
            totalDistance += points.get(i - 1).distanceTo(points.get(i));
        }
        
        double currentDistance = 0;
        for (int i = 0; i < points.size(); i++) {
            double timestamp = i == 0 ? 0 : currentDistance / totalDistance;
            float speed = 1.0f; // Default speed, can be adjusted
            
            path.addWaypoint(new Waypoint(points.get(i), timestamp, speed));
            
            if (i < points.size() - 1) {
                currentDistance += points.get(i).distanceTo(points.get(i + 1));
            }
        }
        
        return path;
    }
    
    /**
     * Set default interpolation type
     */
    public void setDefaultType(InterpolationType type) {
        this.defaultType = type;
        LOGGER.info("Default interpolation type set to: " + type);
    }
    
    /**
     * Set smoothing factor
     */
    public void setSmoothingFactor(float factor) {
        this.smoothingFactor = Math.max(0.0f, Math.min(1.0f, factor));
        LOGGER.info("Smoothing factor set to: " + smoothingFactor);
    }
    
    /**
     * Enable or disable adaptive smoothing
     */
    public void setAdaptiveSmoothing(boolean enabled) {
        this.adaptiveSmoothing = enabled;
        LOGGER.info("Adaptive smoothing " + (enabled ? "enabled" : "disabled"));
    }
    
    /**
     * Clear position history
     */
    public void clearHistory() {
        positionHistory.clear();
        LOGGER.info("Position history cleared");
    }
    
    /**
     * Get interpolation configuration
     */
    public String getConfiguration() {
        return String.format("InterpolationEngine Configuration:\n" +
                           "  Default Type: %s\n" +
                           "  Smoothing Factor: %.2f\n" +
                           "  Adaptive Smoothing: %b\n" +
                           "  History Size: %d",
                           defaultType, smoothingFactor, adaptiveSmoothing, positionHistory.size());
    }
}
