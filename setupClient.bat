@echo off
echo ========================================
echo    JasperGoTo - Setup Cliente Interno
echo ========================================
echo.

REM Verificar Java
echo Verificando versao do Java...
java -version
echo.

echo Java 8 detectado! Continuando setup...
echo.

echo [1/3] Configurando ambiente de desenvolvimento...
call gradlew setupDecompWorkspace --refresh-dependencies

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERRO: Falha no setup do workspace.
    echo Tentando novamente...
    call gradlew setupDecompWorkspace
    if %ERRORLEVEL% NEQ 0 (
        echo ERRO: Setup falhou. Tentando executar diretamente...
        goto :skip_setup
    )
)

echo.
echo [2/3] Configurando IDE...
call gradlew genEclipseRuns

echo.
echo [3/3] Preparando cliente de teste...
call gradlew build

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo AVISO: Build falhou, mas o mod pode ainda funcionar.
)

:skip_setup
echo.
echo ========================================
echo    Setup Completo!
echo ========================================
echo.
echo Para testar o mod:
echo 1. Execute: runClient.bat
echo 2. No jogo, use: /jaspergoto x y z
echo.
echo Comandos disponiveis:
echo - /jaspergoto x y z  : Vai ate as coordenadas
echo - /jaspergoto stop   : Para o movimento
echo - /jaspergoto clear  : Limpa o caminho
echo.
echo Funcionalidades:
echo - Pathfinding automatico com A*
echo - Visualizacao do trajeto em tempo real
echo - Sistema anti-travamento
echo - Deteccao automatica de pulos
echo.
pause