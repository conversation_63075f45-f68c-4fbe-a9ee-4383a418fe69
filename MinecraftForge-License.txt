Minecraft Forge Public Licence
==============================

Version 1.0

0. Definitions
--------------

Minecraft: Denotes a copy of the Minecraft game licensed by Mojang AB

User: Anybody that interract with the software in one of the following ways:
   - play
   - decompile
   - recompile or compile
   - modify

Minecraft Forge: The Minecraft Forge code, in source form, class file form, as
obtained in a standalone fashion or as part of a wider distribution.

Dependency: Code required to have Minecraft Forge working properly. That can
include dependencies required to compile the code as well as modifications in 
the Minecraft sources that are required to have Minecraft Forge working.

1. Scope
--------

The present license is granted to any user of Minecraft Forge, for all files included 
unless stated otherwise in the file itself. As a prerequisite, a user of Minecraft 
Forge must own a legally aquired copy of Minecraft.

2. Play rights
--------------

The user of Minecraft Forge is allowed to install the software on a client or
a server and to play it without restriction.

3. Modification rights
----------------------

The user has the right to decompile the source code, look at either the 
decompiled version or the original source code, and to modify it.

4. Derivation rights
--------------------

The user has the rights to derive code from Minecraft Forge, that is to say to
write code that either extends Minecraft Forge class and interfaces, 
instantiate the objects declared or calls the functions. This code is known as
"derived" code, and can be licensed with conditions different from Minecraft
Forge.


5. Distribution rights
----------------------

The user of Minecraft Forge is allowed to redistribute Minecraft Forge in 
partially, in totallity, or included in a distribution. When distributing 
binaries or class files, the user must provide means to obtain the sources of
the distributed version of Minecraft Forge at no costs. This includes the
files as well as any dependency that the code may rely on, including patches to
minecraft original sources.

Modification of Minecraft Forge as well as dependencies, including patches to
minecraft original sources, has to remain under the terms of the present
license.

The right to distribute Minecraft Forge does not extend to the right to distribute
MCP data files included within Minecraft Forge. These are the property of the MCP
project and should be removed from any customized distribution of Minecraft Forge
or permission sought separately from the MCP team.
