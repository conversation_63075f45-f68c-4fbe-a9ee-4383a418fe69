@echo off
echo ========================================
echo   JasperGoTo - Setup de Ambiente
echo ========================================
echo.

echo [1/4] Verificando Java...
java -version 2>&1 | findstr /i "version"
echo.

echo [2/4] Configurando workspace de desenvolvimento...
echo Este processo pode demorar alguns minutos na primeira vez...
echo.

REM Tenta configurar com gradlew
call gradlew.bat setupDecompWorkspace --refresh-dependencies

if %errorlevel% neq 0 (
    echo.
    echo ========================================
    echo   ERRO: Configuracao falhou!
    echo ========================================
    echo.
    echo Por favor, instale Java 8 JDK:
    echo https://www.oracle.com/java/technologies/javase/javase8-archive-downloads.html
    echo.
    echo Ou use AdoptOpenJDK 8:
    echo https://adoptopenjdk.net/releases.html?variant=openjdk8
    echo.
    pause
    exit /b 1
)

echo.
echo [3/4] Preparando IDE...
call gradlew.bat eclipse

echo.
echo [4/4] Compilando mod...
call gradlew.bat build

echo.
echo ========================================
echo   Setup Completo!
echo ========================================
echo.
echo Para testar o mod, execute: runClient.bat
echo.
pause
