import java.awt.Robot;
import java.awt.AWTException;
import java.awt.event.KeyEvent;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * Simulates keyboard inputs for player movement controls.
 * Handles W, A, S, D for movement, Space for jump, Shift for sneak, and Control for sprint.
 */
public class InputSimulator {
    private static final Logger LOGGER = Logger.getLogger(InputSimulator.class.getName());
    
    // Key definitions
    public enum Key {
        W(KeyEvent.VK_W, "Forward"),
        A(KeyEvent.VK_A, "Left"),
        S(KeyEvent.VK_S, "Backward"),
        D(KeyEvent.VK_D, "Right"),
        SPACE(KeyEvent.VK_SPACE, "Jump"),
        LEFT_SHIFT(KeyEvent.VK_SHIFT, "Sneak"),
        LEFT_CONTROL(KeyEvent.VK_CONTROL, "Sprint"),
        ESCAPE(KeyEvent.VK_ESCAPE, "Menu"),
        E(KeyEvent.VK_E, "Interact"),
        Q(KeyEvent.VK_Q, "Drop"),
        TAB(KeyEvent.VK_TAB, "PlayerList"),
        F(KeyEvent.VK_F, "SwapHands"),
        
        // Number keys for hotbar
        NUM_1(KeyEvent.VK_1, "Hotbar1"),
        NUM_2(KeyEvent.VK_2, "Hotbar2"),
        NUM_3(KeyEvent.VK_3, "Hotbar3"),
        NUM_4(KeyEvent.VK_4, "Hotbar4"),
        NUM_5(KeyEvent.VK_5, "Hotbar5"),
        NUM_6(KeyEvent.VK_6, "Hotbar6"),
        NUM_7(KeyEvent.VK_7, "Hotbar7"),
        NUM_8(KeyEvent.VK_8, "Hotbar8"),
        NUM_9(KeyEvent.VK_9, "Hotbar9");
        
        private final int keyCode;
        private final String description;
        
        Key(int keyCode, String description) {
            this.keyCode = keyCode;
            this.description = description;
        }
        
        public int getKeyCode() {
            return keyCode;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // Key state tracking
    private static class KeyState {
        public boolean isPressed;
        public long lastPressTime;
        public long lastReleaseTime;
        public int pressCount;
        
        public KeyState() {
            this.isPressed = false;
            this.lastPressTime = 0;
            this.lastReleaseTime = 0;
            this.pressCount = 0;
        }
    }
    
    // Robot for simulating key events
    private Robot robot;
    
    // Key states
    private final ConcurrentHashMap<Key, KeyState> keyStates;
    
    // Configuration
    private static final int KEY_PRESS_DELAY = 30; // milliseconds between press and release for tap
    private static final int ANTI_SPAM_DELAY = 50; // minimum milliseconds between same key presses
    private static final int HUMAN_REACTION_VARIANCE = 20; // variance in milliseconds for human-like input
    
    // Threading
    private final ScheduledExecutorService scheduler;
    private volatile boolean enabled;
    
    // Input queue for complex sequences
    private final ConcurrentHashMap<String, InputSequence> inputSequences;
    
    /**
     * Input sequence for complex movements
     */
    private static class InputSequence {
        public final Key[] keys;
        public final long[] durations;
        public final boolean[] hold;
        
        public InputSequence(Key[] keys, long[] durations, boolean[] hold) {
            this.keys = keys;
            this.durations = durations;
            this.hold = hold;
        }
    }
    
    /**
     * Constructor initializes the input simulator
     */
    public InputSimulator() {
        try {
            this.robot = new Robot();
            this.robot.setAutoDelay(5); // Small delay for stability
            this.robot.setAutoWaitForIdle(true);
        } catch (AWTException e) {
            LOGGER.log(Level.SEVERE, "Failed to create Robot instance", e);
            throw new RuntimeException("Cannot initialize InputSimulator without Robot", e);
        }
        
        this.keyStates = new ConcurrentHashMap<>();
        for (Key key : Key.values()) {
            keyStates.put(key, new KeyState());
        }
        
        this.scheduler = Executors.newScheduledThreadPool(2);
        this.inputSequences = new ConcurrentHashMap<>();
        this.enabled = true;
        
        LOGGER.info("Input simulator initialized");
    }
    
    /**
     * Press a key (and hold it)
     */
    public void pressKey(Key key) {
        if (!enabled) return;
        
        KeyState state = keyStates.get(key);
        if (state == null) return;
        
        // Avoid spamming the same key
        long currentTime = System.currentTimeMillis();
        if (state.isPressed || (currentTime - state.lastPressTime < ANTI_SPAM_DELAY)) {
            return;
        }
        
        // Add human-like variance
        int variance = (int)(Math.random() * HUMAN_REACTION_VARIANCE);
        
        scheduler.schedule(() -> {
            try {
                robot.keyPress(key.getKeyCode());
                state.isPressed = true;
                state.lastPressTime = System.currentTimeMillis();
                state.pressCount++;
                LOGGER.fine("Pressed key: " + key.getDescription());
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "Failed to press key: " + key, e);
            }
        }, variance, TimeUnit.MILLISECONDS);
    }
    
    /**
     * Release a key
     */
    public void releaseKey(Key key) {
        if (!enabled) return;
        
        KeyState state = keyStates.get(key);
        if (state == null || !state.isPressed) return;
        
        // Add human-like variance
        int variance = (int)(Math.random() * HUMAN_REACTION_VARIANCE);
        
        scheduler.schedule(() -> {
            try {
                robot.keyRelease(key.getKeyCode());
                state.isPressed = false;
                state.lastReleaseTime = System.currentTimeMillis();
                LOGGER.fine("Released key: " + key.getDescription());
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "Failed to release key: " + key, e);
            }
        }, variance, TimeUnit.MILLISECONDS);
    }
    
    /**
     * Tap a key (press and release quickly)
     */
    public void tapKey(Key key) {
        tapKey(key, KEY_PRESS_DELAY);
    }
    
    /**
     * Tap a key with custom duration
     */
    public void tapKey(Key key, int duration) {
        if (!enabled) return;
        
        pressKey(key);
        scheduler.schedule(() -> releaseKey(key), duration, TimeUnit.MILLISECONDS);
    }
    
    /**
     * Double tap a key (for sprinting)
     */
    public void doubleTapKey(Key key) {
        if (!enabled) return;
        
        tapKey(key);
        scheduler.schedule(() -> tapKey(key), 100, TimeUnit.MILLISECONDS);
    }
    
    /**
     * Hold multiple keys simultaneously
     */
    public void holdKeys(Key... keys) {
        for (Key key : keys) {
            pressKey(key);
        }
    }
    
    /**
     * Release multiple keys simultaneously
     */
    public void releaseKeys(Key... keys) {
        for (Key key : keys) {
            releaseKey(key);
        }
    }
    
    /**
     * Release all currently pressed keys
     */
    public void releaseAllKeys() {
        for (Key key : Key.values()) {
            if (isKeyPressed(key)) {
                releaseKey(key);
            }
        }
        LOGGER.info("Released all keys");
    }
    
    /**
     * Check if a key is currently pressed
     */
    public boolean isKeyPressed(Key key) {
        KeyState state = keyStates.get(key);
        return state != null && state.isPressed;
    }
    
    /**
     * Perform a movement combo (e.g., strafe-jump)
     */
    public void performMovementCombo(String comboName, Key[] keys, long[] durations) {
        if (!enabled || keys.length != durations.length) return;
        
        long totalDelay = 0;
        for (int i = 0; i < keys.length; i++) {
            final Key key = keys[i];
            final long duration = durations[i];
            
            scheduler.schedule(() -> pressKey(key), totalDelay, TimeUnit.MILLISECONDS);
            totalDelay += duration;
            scheduler.schedule(() -> releaseKey(key), totalDelay, TimeUnit.MILLISECONDS);
            totalDelay += 50; // Small gap between keys
        }
        
        LOGGER.info("Performing movement combo: " + comboName);
    }
    
    /**
     * Simulate walking forward for a duration
     */
    public void walkForward(long durationMs) {
        pressKey(Key.W);
        scheduler.schedule(() -> releaseKey(Key.W), durationMs, TimeUnit.MILLISECONDS);
    }
    
    /**
     * Simulate sprinting forward for a duration
     */
    public void sprintForward(long durationMs) {
        holdKeys(Key.W, Key.LEFT_CONTROL);
        scheduler.schedule(() -> releaseKeys(Key.W, Key.LEFT_CONTROL), 
                          durationMs, TimeUnit.MILLISECONDS);
    }
    
    /**
     * Simulate strafing (moving sideways)
     */
    public void strafe(boolean left, long durationMs) {
        Key strafeKey = left ? Key.A : Key.D;
        pressKey(strafeKey);
        scheduler.schedule(() -> releaseKey(strafeKey), durationMs, TimeUnit.MILLISECONDS);
    }
    
    /**
     * Simulate diagonal movement
     */
    public void moveDiagonal(boolean forward, boolean left, long durationMs) {
        Key forwardKey = forward ? Key.W : Key.S;
        Key sideKey = left ? Key.A : Key.D;
        
        holdKeys(forwardKey, sideKey);
        scheduler.schedule(() -> releaseKeys(forwardKey, sideKey), 
                          durationMs, TimeUnit.MILLISECONDS);
    }
    
    /**
     * Simulate bunny hopping (repeated jumping while moving)
     */
    public void bunnyHop(int hops, long hopInterval) {
        pressKey(Key.W); // Start moving forward
        
        for (int i = 0; i < hops; i++) {
            long delay = i * hopInterval;
            scheduler.schedule(() -> tapKey(Key.SPACE, 50), delay, TimeUnit.MILLISECONDS);
        }
        
        scheduler.schedule(() -> releaseKey(Key.W), 
                          hops * hopInterval, TimeUnit.MILLISECONDS);
    }
    
    /**
     * Simulate circle strafing (moving in a circle around a point)
     */
    public void circleStrafe(boolean clockwise, long durationMs) {
        Key strafeKey = clockwise ? Key.D : Key.A;
        holdKeys(Key.W, strafeKey);
        
        // Also need to rotate camera - would coordinate with RotationController
        scheduler.schedule(() -> releaseKeys(Key.W, strafeKey), 
                          durationMs, TimeUnit.MILLISECONDS);
    }
    
    /**
     * Quick stop (release all movement keys)
     */
    public void quickStop() {
        releaseKeys(Key.W, Key.A, Key.S, Key.D, Key.LEFT_CONTROL, Key.LEFT_SHIFT);
    }
    
    /**
     * Select hotbar slot
     */
    public void selectHotbarSlot(int slot) {
        if (slot < 1 || slot > 9) {
            LOGGER.warning("Invalid hotbar slot: " + slot);
            return;
        }
        
        Key hotbarKey = Key.valueOf("NUM_" + slot);
        tapKey(hotbarKey);
    }
    
    /**
     * Enable or disable input simulation
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        if (!enabled) {
            releaseAllKeys();
        }
        LOGGER.info("Input simulation " + (enabled ? "enabled" : "disabled"));
    }
    
    /**
     * Get statistics for a key
     */
    public String getKeyStatistics(Key key) {
        KeyState state = keyStates.get(key);
        if (state == null) return "No data for key: " + key;
        
        return String.format("Key %s: Pressed=%b, PressCount=%d, LastPress=%d, LastRelease=%d",
                           key.getDescription(), state.isPressed, state.pressCount,
                           state.lastPressTime, state.lastReleaseTime);
    }
    
    /**
     * Reset all key states
     */
    public void reset() {
        releaseAllKeys();
        for (KeyState state : keyStates.values()) {
            state.pressCount = 0;
            state.lastPressTime = 0;
            state.lastReleaseTime = 0;
        }
        LOGGER.info("Input simulator reset");
    }
    
    /**
     * Shutdown the input simulator
     */
    public void shutdown() {
        enabled = false;
        releaseAllKeys();
        
        try {
            scheduler.shutdown();
            if (!scheduler.awaitTermination(2, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        LOGGER.info("Input simulator shutdown");
    }
}
