@echo off
echo ========================================
echo    JasperGoTo - Setup com Java 8
echo ========================================
echo.

REM Definir Java 8 explicitamente
set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_461
if not exist "%JAVA_HOME%" set JAVA_HOME=C:\Program Files\Java\jre1.8.0_461
if not exist "%JAVA_HOME%" set JAVA_HOME=C:\Program Files (x86)\Java\jdk1.8.0_461
if not exist "%JAVA_HOME%" set JAVA_HOME=C:\Program Files (x86)\Java\jre1.8.0_461

if exist "%JAVA_HOME%" (
    echo Java 8 encontrado em: %JAVA_HOME%
    set PATH=%JAVA_HOME%\bin;%PATH%
) else (
    echo ERRO: Java 8 nao encontrado!
    echo Por favor instale Java 8 para continuar.
    pause
    exit /b 1
)

echo Verificando Java...
java -version
echo.

echo Configurando workspace...
gradlew setupDecompWorkspace

echo.
echo Gerando configuracoes...
gradlew genEclipseRuns

echo.
echo Setup concluido!
echo Execute runClient.bat para testar o mod.
echo.
pause