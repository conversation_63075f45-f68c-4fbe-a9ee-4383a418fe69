@echo off
echo ========================================
echo    JasperGoTo - Teste Rápido
echo ========================================
echo.

echo Executando cliente de teste diretamente...
echo (Ignorando problemas de build)
echo.

REM Tentar usar Java 8 se estiver disponível
set JAVA8_HOME=C:\Program Files\Java\jre1.8.0_351
if exist "%JAVA8_HOME%\bin\java.exe" (
    echo Usando Java 8...
    set JAVA_HOME=%JAVA8_HOME%
    set PATH=%JAVA8_HOME%\bin;%PATH%
)

echo Comandos do mod:
echo - /jaspergoto x y z  : Vai até as coordenadas
echo - /jaspergoto stop   : Para o movimento
echo - /jaspergoto clear  : Limpa o caminho
echo.

REM Executar diretamente
gradlew runClient

echo.
echo Teste finalizado.
pause