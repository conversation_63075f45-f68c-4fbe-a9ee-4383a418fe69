@echo off
echo ========================================
echo    Teste de Compilacao com Java 8
echo ========================================
echo.

set "JAVA_HOME=C:\Program Files\Java\jdk1.8.0_333"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo Usando Java:
java -version
echo.

echo Limpando builds anteriores...
if exist "build" rmdir /s /q build

echo.
echo Compilando com Gradle usando Java 8 diretamente...
"%JAVA_HOME%\bin\java.exe" -jar gradle/wrapper/gradle-wrapper.jar build --no-daemon --stacktrace

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo    SUCESSO! Mod compilado corretamente
    echo ========================================
    echo.
    echo Arquivos gerados:
    dir build\libs\*.jar
    echo.
) else (
    echo.
    echo ========================================
    echo    FALHA NA COMPILACAO
    echo ========================================
    echo.
    echo Verifique as instrucoes em INSTRUCOES_COMPILAR.md
    echo.
)

pause