@echo off
echo ========================================
echo    Criando JAR do JasperGoTo
echo ========================================
echo.

REM Criar estrutura de diretórios
if not exist "build" mkdir build
if not exist "build\jar" mkdir build\jar
if not exist "build\jar\com" mkdir build\jar\com
if not exist "build\jar\com\jaspergoto" mkdir build\jar\com\jaspergoto
if not exist "build\jar\com\jaspergoto\commands" mkdir build\jar\com\jaspergoto\commands
if not exist "build\jar\com\jaspergoto\pathfinding" mkdir build\jar\com\jaspergoto\pathfinding

echo Copiando arquivos fonte...

REM Copiar arquivos Java (como fonte, já que não conseguimos compilar)
copy "src\main\java\com\jaspergoto\*.java" "build\jar\com\jaspergoto\"
copy "src\main\java\com\jaspergoto\commands\*.java" "build\jar\com\jaspergoto\commands\"
copy "src\main\java\com\jaspergoto\pathfinding\*.java" "build\jar\com\jaspergoto\pathfinding\"

REM Copiar mcmod.info
copy "src\main\resources\mcmod.info" "build\jar\"

echo Criando manifest...

REM Criar MANIFEST.MF
echo Manifest-Version: 1.0 > build\jar\META-INF\MANIFEST.MF
echo FMLCorePlugin: com.jaspergoto.JasperGoTo >> build\jar\META-INF\MANIFEST.MF

echo Criando JAR...

REM Criar JAR
cd build\jar
jar cf ..\jaspergoto-1.0.0.jar *
cd ..\..

echo.
echo ========================================
echo    JAR CRIADO!
echo ========================================
echo.
echo Arquivo: build\jaspergoto-1.0.0.jar
echo.
echo IMPORTANTE: Este JAR contém código fonte.
echo Para funcionar, você precisa:
echo 1. Minecraft 1.8.9
echo 2. Minecraft Forge 11.15.1.2318
echo 3. Colocar o JAR na pasta mods
echo.
echo Comandos do mod:
echo - /jaspergoto x y z  : Vai até coordenadas
echo - /jaspergoto stop   : Para movimento
echo - /jaspergoto clear  : Limpa caminho
echo.
pause