@echo off
echo ========================================
echo    Compilacao Forcada com Java 8
echo ========================================
echo.

REM Definir Java 8 explicitamente
set "JAVA_HOME=C:\Program Files\Java\jdk1.8.0_333"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo Usando Java 8: %JAVA_HOME%
java -version
echo.

echo Tentando compilacao forcada...

REM Tentar com gradle wrapper usando Java 8
set GRADLE_OPTS=-Dorg.gradle.java.home="%JAVA_HOME%"

echo Executando: gradlew build --no-daemon --stacktrace
gradlew build --no-daemon --stacktrace

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo    COMPILACAO SUCESSO!
    echo ========================================
    echo.
    echo JAR criado em: build\libs\
    dir build\libs\*.jar
) else (
    echo.
    echo ========================================
    echo    COMPILACAO FALHOU
    echo ========================================
    echo.
    echo Use o IntelliJ para compilar:
    echo 1. Build → Rebuild Project
    echo 2. Build → Build Artifacts
    echo 3. Crie JAR manualmente
)

echo.
pause