import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Adjusts movement speed based on terrain type and environmental conditions.
 * Provides realistic speed modifiers for different blocks and situations.
 */
public class SpeedAdapter {
    private static final Logger LOGGER = Logger.getLogger(SpeedAdapter.class.getName());
    
    /**
     * Terrain types that affect movement speed
     */
    public enum TerrainType {
        // Normal terrain
        GRASS_BLOCK(1.0f, "Normal speed on grass"),
        DIRT(1.0f, "Normal speed on dirt"),
        STONE(1.0f, "Normal speed on stone"),
        SAND(0.8f, "Slower on sand"),
        GRAVEL(0.9f, "Slightly slower on gravel"),
        
        // Slippery surfaces
        ICE(1.5f, "Faster but less control on ice"),
        PACKED_ICE(1.6f, "Very fast on packed ice"),
        BLUE_ICE(1.7f, "Extremely fast on blue ice"),
        
        // Slow surfaces
        SOUL_SAND(0.5f, "Very slow on soul sand"),
        SOUL_SOIL(0.6f, "Slow on soul soil"),
        HONEY_BLOCK(0.4f, "Extremely slow on honey"),
        SLIME_BLOCK(1.2f, "Bouncy speed on slime"),
        
        // Water and liquids
        WATER(0.5f, "Slow swimming speed"),
        LAVA(0.3f, "Very slow in lava"),
        
        // Special blocks
        COBWEB(0.15f, "Extremely slow in cobwebs"),
        LADDER(0.6f, "Climbing speed"),
        VINES(0.7f, "Vine climbing speed"),
        SCAFFOLDING(0.8f, "Scaffolding movement"),
        
        // Path blocks
        GRASS_PATH(1.0f, "Normal speed on paths"),
        FARMLAND(0.95f, "Slightly slower on farmland"),
        
        // Nether blocks
        NETHERRACK(0.95f, "Slightly slower on netherrack"),
        MAGMA_BLOCK(0.7f, "Slower on magma blocks"),
        
        // End blocks
        END_STONE(1.0f, "Normal speed on end stone"),
        PURPUR_BLOCK(1.0f, "Normal speed on purpur"),
        
        // Default
        UNKNOWN(1.0f, "Default speed for unknown terrain");
        
        private final float speedMultiplier;
        private final String description;
        
        TerrainType(float speedMultiplier, String description) {
            this.speedMultiplier = speedMultiplier;
            this.description = description;
        }
        
        public float getSpeedMultiplier() {
            return speedMultiplier;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * Environmental effects that modify speed
     */
    public enum EnvironmentalEffect {
        NONE(1.0f),
        RAIN(0.95f),
        SNOW(0.9f),
        STORM(0.85f),
        NIGHT_VISION(1.05f),
        BLINDNESS(0.7f),
        SPEED_POTION_1(1.2f),
        SPEED_POTION_2(1.4f),
        SLOWNESS_POTION_1(0.85f),
        SLOWNESS_POTION_2(0.6f),
        HASTE_1(1.1f),
        HASTE_2(1.2f),
        MINING_FATIGUE(0.7f),
        DOLPHIN_GRACE(1.3f),
        DEPTH_STRIDER_1(1.33f),
        DEPTH_STRIDER_2(1.67f),
        DEPTH_STRIDER_3(2.0f),
        FROST_WALKER(1.1f),
        SOUL_SPEED_1(1.2f),
        SOUL_SPEED_2(1.4f),
        SOUL_SPEED_3(1.6f);
        
        private final float modifier;
        
        EnvironmentalEffect(float modifier) {
            this.modifier = modifier;
        }
        
        public float getModifier() {
            return modifier;
        }
    }
    
    // Cache for terrain lookups
    private final Map<String, TerrainType> terrainCache;
    
    // Current environmental effects
    private final Map<EnvironmentalEffect, Boolean> activeEffects;
    
    // Configuration
    private boolean adaptiveSpeedEnabled;
    private float globalSpeedMultiplier;
    private float maxSpeedCap;
    private float minSpeedFloor;
    
    /**
     * Constructor initializes the speed adapter
     */
    public SpeedAdapter() {
        this.terrainCache = new HashMap<>();
        this.activeEffects = new HashMap<>();
        this.adaptiveSpeedEnabled = true;
        this.globalSpeedMultiplier = 1.0f;
        this.maxSpeedCap = 2.5f;
        this.minSpeedFloor = 0.1f;
        
        initializeTerrainCache();
        LOGGER.info("Speed adapter initialized");
    }
    
    /**
     * Initialize terrain cache with common block mappings
     */
    private void initializeTerrainCache() {
        // Map block IDs to terrain types
        terrainCache.put("minecraft:grass_block", TerrainType.GRASS_BLOCK);
        terrainCache.put("minecraft:dirt", TerrainType.DIRT);
        terrainCache.put("minecraft:stone", TerrainType.STONE);
        terrainCache.put("minecraft:sand", TerrainType.SAND);
        terrainCache.put("minecraft:gravel", TerrainType.GRAVEL);
        terrainCache.put("minecraft:ice", TerrainType.ICE);
        terrainCache.put("minecraft:packed_ice", TerrainType.PACKED_ICE);
        terrainCache.put("minecraft:blue_ice", TerrainType.BLUE_ICE);
        terrainCache.put("minecraft:soul_sand", TerrainType.SOUL_SAND);
        terrainCache.put("minecraft:soul_soil", TerrainType.SOUL_SOIL);
        terrainCache.put("minecraft:honey_block", TerrainType.HONEY_BLOCK);
        terrainCache.put("minecraft:slime_block", TerrainType.SLIME_BLOCK);
        terrainCache.put("minecraft:water", TerrainType.WATER);
        terrainCache.put("minecraft:lava", TerrainType.LAVA);
        terrainCache.put("minecraft:cobweb", TerrainType.COBWEB);
        terrainCache.put("minecraft:ladder", TerrainType.LADDER);
        terrainCache.put("minecraft:vine", TerrainType.VINES);
        terrainCache.put("minecraft:scaffolding", TerrainType.SCAFFOLDING);
        terrainCache.put("minecraft:grass_path", TerrainType.GRASS_PATH);
        terrainCache.put("minecraft:farmland", TerrainType.FARMLAND);
        terrainCache.put("minecraft:netherrack", TerrainType.NETHERRACK);
        terrainCache.put("minecraft:magma_block", TerrainType.MAGMA_BLOCK);
        terrainCache.put("minecraft:end_stone", TerrainType.END_STONE);
        terrainCache.put("minecraft:purpur_block", TerrainType.PURPUR_BLOCK);
    }
    
    /**
     * Adjust speed based on current position and terrain
     */
    public double adjustSpeed(double baseSpeed, MovementController.Position position) {
        if (!adaptiveSpeedEnabled) {
            return baseSpeed * globalSpeedMultiplier;
        }
        
        // Get terrain at position (would integrate with world data)
        TerrainType terrain = getTerrainAtPosition(position);
        
        // Calculate cumulative speed modifier
        float speedModifier = terrain.getSpeedMultiplier();
        
        // Apply environmental effects
        speedModifier *= getEnvironmentalModifier();
        
        // Apply global multiplier
        speedModifier *= globalSpeedMultiplier;
        
        // Apply caps
        speedModifier = Math.max(minSpeedFloor, Math.min(maxSpeedCap, speedModifier));
        
        double adjustedSpeed = baseSpeed * speedModifier;
        
        // Log significant speed changes
        if (Math.abs(speedModifier - 1.0f) > 0.1f) {
            LOGGER.fine(String.format("Speed adjusted: %.2f -> %.2f (modifier: %.2f, terrain: %s)",
                                     baseSpeed, adjustedSpeed, speedModifier, terrain.name()));
        }
        
        return adjustedSpeed;
    }
    
    /**
     * Get terrain type at a specific position
     */
    private TerrainType getTerrainAtPosition(MovementController.Position position) {
        // This would integrate with the game world data
        // For now, return a simulated terrain based on position
        
        // Simulate different terrain zones
        if (position.y < 60) {
            // Underground
            return TerrainType.STONE;
        } else if (position.y > 100) {
            // Mountains
            return TerrainType.STONE;
        } else if (Math.abs(position.x) > 1000 || Math.abs(position.z) > 1000) {
            // Far lands - maybe ice
            return TerrainType.ICE;
        } else {
            // Normal overworld
            return TerrainType.GRASS_BLOCK;
        }
    }
    
    /**
     * Get terrain type from block ID
     */
    public TerrainType getTerrainFromBlock(String blockId) {
        return terrainCache.getOrDefault(blockId, TerrainType.UNKNOWN);
    }
    
    /**
     * Calculate environmental speed modifier
     */
    private float getEnvironmentalModifier() {
        float modifier = 1.0f;
        
        for (Map.Entry<EnvironmentalEffect, Boolean> entry : activeEffects.entrySet()) {
            if (entry.getValue()) {
                modifier *= entry.getKey().getModifier();
            }
        }
        
        return modifier;
    }
    
    /**
     * Set an environmental effect active or inactive
     */
    public void setEnvironmentalEffect(EnvironmentalEffect effect, boolean active) {
        activeEffects.put(effect, active);
        LOGGER.info("Environmental effect " + effect + " set to " + active);
    }
    
    /**
     * Clear all environmental effects
     */
    public void clearEnvironmentalEffects() {
        activeEffects.clear();
        LOGGER.info("All environmental effects cleared");
    }
    
    /**
     * Calculate speed for specific movement type
     */
    public double calculateMovementSpeed(MovementController.MovementState state, 
                                        TerrainType terrain, 
                                        boolean hasEffects) {
        double baseSpeed;
        
        // Base speed for movement state
        switch (state) {
            case RUNNING:
                baseSpeed = 5.6;
                break;
            case SNEAKING:
                baseSpeed = 1.3;
                break;
            case SWIMMING:
                baseSpeed = 2.0;
                break;
            case FLYING:
                baseSpeed = 10.0;
                break;
            case WALKING:
            default:
                baseSpeed = 4.3;
                break;
        }
        
        // Apply terrain modifier
        baseSpeed *= terrain.getSpeedMultiplier();
        
        // Apply effects if present
        if (hasEffects) {
            baseSpeed *= getEnvironmentalModifier();
        }
        
        return baseSpeed;
    }
    
    /**
     * Check if movement should be slowed on current terrain
     */
    public boolean shouldSlowMovement(TerrainType terrain) {
        return terrain.getSpeedMultiplier() < 0.8f;
    }
    
    /**
     * Check if movement should be accelerated on current terrain
     */
    public boolean shouldAccelerateMovement(TerrainType terrain) {
        return terrain.getSpeedMultiplier() > 1.2f;
    }
    
    /**
     * Get friction value for terrain (affects stopping distance)
     */
    public float getTerrainFriction(TerrainType terrain) {
        switch (terrain) {
            case ICE:
            case PACKED_ICE:
            case BLUE_ICE:
                return 0.98f; // Very slippery
            case SLIME_BLOCK:
                return 0.8f; // Bouncy
            case HONEY_BLOCK:
                return 0.4f; // Very sticky
            case SOUL_SAND:
            case SOUL_SOIL:
                return 0.6f; // Sticky
            default:
                return 0.91f; // Normal friction
        }
    }
    
    /**
     * Calculate jump height modifier for terrain
     */
    public float getJumpHeightModifier(TerrainType terrain) {
        switch (terrain) {
            case SLIME_BLOCK:
                return 1.5f; // Higher jumps on slime
            case HONEY_BLOCK:
                return 0.5f; // Lower jumps on honey
            case SOUL_SAND:
                return 0.8f; // Slightly lower jumps
            default:
                return 1.0f; // Normal jump height
        }
    }
    
    /**
     * Enable or disable adaptive speed
     */
    public void setAdaptiveSpeedEnabled(boolean enabled) {
        this.adaptiveSpeedEnabled = enabled;
        LOGGER.info("Adaptive speed " + (enabled ? "enabled" : "disabled"));
    }
    
    /**
     * Set global speed multiplier
     */
    public void setGlobalSpeedMultiplier(float multiplier) {
        this.globalSpeedMultiplier = Math.max(0.1f, Math.min(3.0f, multiplier));
        LOGGER.info("Global speed multiplier set to " + this.globalSpeedMultiplier);
    }
    
    /**
     * Get current speed configuration
     */
    public String getSpeedConfiguration() {
        StringBuilder config = new StringBuilder();
        config.append("Speed Adapter Configuration:\n");
        config.append("  Adaptive Speed: ").append(adaptiveSpeedEnabled).append("\n");
        config.append("  Global Multiplier: ").append(globalSpeedMultiplier).append("\n");
        config.append("  Max Speed Cap: ").append(maxSpeedCap).append("\n");
        config.append("  Min Speed Floor: ").append(minSpeedFloor).append("\n");
        config.append("  Active Effects: ").append(activeEffects.size()).append("\n");
        
        for (Map.Entry<EnvironmentalEffect, Boolean> entry : activeEffects.entrySet()) {
            if (entry.getValue()) {
                config.append("    - ").append(entry.getKey()).append(": ")
                      .append(entry.getKey().getModifier()).append("\n");
            }
        }
        
        return config.toString();
    }
    
    /**
     * Reset to default configuration
     */
    public void reset() {
        adaptiveSpeedEnabled = true;
        globalSpeedMultiplier = 1.0f;
        maxSpeedCap = 2.5f;
        minSpeedFloor = 0.1f;
        activeEffects.clear();
        LOGGER.info("Speed adapter reset to defaults");
    }
}
