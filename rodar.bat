@echo off
echo ========================================
echo   JasperGoTo - Executar Cliente
echo ========================================
echo.

REM Configurar Java 8
set "JAVA_HOME=C:\Program Files\Java\jdk1.8.0_333"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo Verificando Java...
java -version
echo.

echo Verificando mod...
if exist "run\mods\jaspergoto-1.0.0.jar" (
    echo ✅ Mod encontrado: jaspergoto-1.0.0.jar
) else (
    echo ❌ Mod nao encontrado! Execute 'executar.bat' primeiro.
    pause
    exit /b 1
)

echo.
echo ========================================
echo        INICIANDO MINECRAFT
echo ========================================
echo.
echo Comandos do mod:
echo   /jaspergoto ^<x^> ^<y^> ^<z^>  - Navegar para coordenadas
echo   /jaspergoto stop           - Parar movimento
echo   /jaspergoto clear          - Limpar caminho
echo.
echo CORRECAO APLICADA:
echo   ✅ Movimento manual funciona normalmente
echo   ✅ Bot nao interfere com controles manuais
echo.

echo Pressione qualquer tecla para iniciar...
pause >nul

echo Executando cliente...
gradlew.bat runClient

echo.
echo Cliente encerrado.
pause
