import java.util.*;
import java.util.stream.Collectors;

/**
 * Calculates optimal paths with terrain analysis.
 * Provides different path calculation strategies and terrain evaluation.
 */
public class PathCalculator {
    
    private final PathfindingEngine pathfindingEngine;
    private final ObstacleDetector obstacleDetector;
    private final PathOptimizer pathOptimizer;
    
    // Path calculation strategies
    public enum PathStrategy {
        SHORTEST,      // Minimize distance
        SAFEST,        // Minimize danger
        FASTEST,       // Minimize time
        BALANCED,      // Balance between safety and speed
        EXPLORATION    // Prefer unexplored areas
    }
    
    // Terrain analysis result
    public static class TerrainAnalysis {
        private final double averageElevation;
        private final double terrainRoughness;
        private final double hazardDensity;
        private final double waterCoverage;
        private final double obstacleCount;
        private final Map<ObstacleDetector.BlockType, Integer> blockDistribution;
        
        public TerrainAnalysis(double averageElevation, double terrainRoughness,
                               double hazardDensity, double waterCoverage,
                               double obstacleCount, Map<ObstacleDetector.BlockType, Integer> blockDistribution) {
            this.averageElevation = averageElevation;
            this.terrainRoughness = terrainRoughness;
            this.hazardDensity = hazardDensity;
            this.waterCoverage = waterCoverage;
            this.obstacleCount = obstacleCount;
            this.blockDistribution = new HashMap<>(blockDistribution);
        }
        
        // Getters
        public double getAverageElevation() { return averageElevation; }
        public double getTerrainRoughness() { return terrainRoughness; }
        public double getHazardDensity() { return hazardDensity; }
        public double getWaterCoverage() { return waterCoverage; }
        public double getObstacleCount() { return obstacleCount; }
        public Map<ObstacleDetector.BlockType, Integer> getBlockDistribution() { 
            return new HashMap<>(blockDistribution); 
        }
        
        @Override
        public String toString() {
            return String.format("TerrainAnalysis[elevation=%.1f, roughness=%.2f, hazards=%.2f%%, water=%.2f%%, obstacles=%d]",
                    averageElevation, terrainRoughness, hazardDensity * 100, waterCoverage * 100, (int)obstacleCount);
        }
    }
    
    // Path calculation result
    public static class CalculatedPath {
        private final List<Node> waypoints;
        private final double totalDistance;
        private final double estimatedTime;
        private final double dangerLevel;
        private final PathStrategy strategy;
        private final TerrainAnalysis terrainAnalysis;
        private final List<ObstacleDetector.MovementType> movementSequence;
        
        public CalculatedPath(List<Node> waypoints, double totalDistance,
                             double estimatedTime, double dangerLevel,
                             PathStrategy strategy, TerrainAnalysis terrainAnalysis,
                             List<ObstacleDetector.MovementType> movementSequence) {
            this.waypoints = new ArrayList<>(waypoints);
            this.totalDistance = totalDistance;
            this.estimatedTime = estimatedTime;
            this.dangerLevel = dangerLevel;
            this.strategy = strategy;
            this.terrainAnalysis = terrainAnalysis;
            this.movementSequence = new ArrayList<>(movementSequence);
        }
        
        // Getters
        public List<Node> getWaypoints() { return new ArrayList<>(waypoints); }
        public double getTotalDistance() { return totalDistance; }
        public double getEstimatedTime() { return estimatedTime; }
        public double getDangerLevel() { return dangerLevel; }
        public PathStrategy getStrategy() { return strategy; }
        public TerrainAnalysis getTerrainAnalysis() { return terrainAnalysis; }
        public List<ObstacleDetector.MovementType> getMovementSequence() { 
            return new ArrayList<>(movementSequence); 
        }
        
        @Override
        public String toString() {
            return String.format("Path[strategy=%s, distance=%.1f, time=%.1fs, danger=%.1f, waypoints=%d]",
                    strategy, totalDistance, estimatedTime, dangerLevel, waypoints.size());
        }
    }
    
    /**
     * Creates a path calculator with specified components.
     */
    public PathCalculator(ObstacleDetector.WorldAccessor world) {
        this.pathfindingEngine = new PathfindingEngine(world);
        this.obstacleDetector = new ObstacleDetector(world);
        this.pathOptimizer = new PathOptimizer();
    }
    
    /**
     * Creates a path calculator with custom pathfinding engine.
     */
    public PathCalculator(PathfindingEngine engine, ObstacleDetector detector) {
        this.pathfindingEngine = engine;
        this.obstacleDetector = detector;
        this.pathOptimizer = new PathOptimizer();
    }
    
    /**
     * Calculates a path using the specified strategy.
     */
    public CalculatedPath calculatePath(Node start, Node goal, PathStrategy strategy) {
        // Configure pathfinding engine based on strategy
        configureEngineForStrategy(strategy);
        
        // Find raw path
        PathfindingEngine.PathResult result = pathfindingEngine.findPath(start, goal);
        
        if (!result.isSuccess()) {
            return null;
        }
        
        List<Node> rawPath = result.getPath();
        
        // Optimize path
        List<Node> optimizedPath = pathOptimizer.optimizePath(rawPath, obstacleDetector);
        
        // Analyze terrain along path
        TerrainAnalysis terrainAnalysis = analyzePathTerrain(optimizedPath);
        
        // Calculate path metrics
        double totalDistance = calculateTotalDistance(optimizedPath);
        double estimatedTime = estimateTraversalTime(optimizedPath);
        double dangerLevel = assessPathDanger(optimizedPath);
        
        // Generate movement sequence
        List<ObstacleDetector.MovementType> movementSequence = 
            generateMovementSequence(optimizedPath);
        
        return new CalculatedPath(optimizedPath, totalDistance, estimatedTime,
                                 dangerLevel, strategy, terrainAnalysis, movementSequence);
    }
    
    /**
     * Calculates multiple paths with different strategies and returns all.
     */
    public List<CalculatedPath> calculateMultiplePaths(Node start, Node goal) {
        List<CalculatedPath> paths = new ArrayList<>();
        
        for (PathStrategy strategy : PathStrategy.values()) {
            CalculatedPath path = calculatePath(start, goal, strategy);
            if (path != null) {
                paths.add(path);
            }
        }
        
        // Sort by total cost (combination of distance and danger)
        paths.sort(Comparator.comparingDouble(p -> 
            p.getTotalDistance() + p.getDangerLevel() * 10));
        
        return paths;
    }
    
    /**
     * Finds alternative paths to avoid a specific area.
     */
    public List<CalculatedPath> findAlternativePaths(Node start, Node goal, 
                                                      Set<Node> avoidArea, int maxPaths) {
        List<CalculatedPath> alternativePaths = new ArrayList<>();
        Set<List<Node>> uniquePaths = new HashSet<>();
        
        // Generate variations by adding waypoints
        List<Node> waypoints = generateWaypoints(start, goal, avoidArea);
        
        for (Node waypoint : waypoints) {
            // Calculate path through waypoint
            CalculatedPath pathToWaypoint = calculatePath(start, waypoint, PathStrategy.BALANCED);
            if (pathToWaypoint == null) continue;
            
            CalculatedPath pathFromWaypoint = calculatePath(waypoint, goal, PathStrategy.BALANCED);
            if (pathFromWaypoint == null) continue;
            
            // Combine paths
            List<Node> combinedPath = new ArrayList<>(pathToWaypoint.getWaypoints());
            combinedPath.addAll(pathFromWaypoint.getWaypoints().subList(1, 
                               pathFromWaypoint.getWaypoints().size()));
            
            // Check if path is unique
            if (uniquePaths.add(combinedPath)) {
                // Optimize combined path
                List<Node> optimized = pathOptimizer.optimizePath(combinedPath, obstacleDetector);
                
                // Create calculated path
                TerrainAnalysis terrain = analyzePathTerrain(optimized);
                double distance = calculateTotalDistance(optimized);
                double time = estimateTraversalTime(optimized);
                double danger = assessPathDanger(optimized);
                List<ObstacleDetector.MovementType> movements = generateMovementSequence(optimized);
                
                alternativePaths.add(new CalculatedPath(optimized, distance, time,
                                                       danger, PathStrategy.BALANCED,
                                                       terrain, movements));
                
                if (alternativePaths.size() >= maxPaths) {
                    break;
                }
            }
        }
        
        return alternativePaths;
    }
    
    /**
     * Analyzes terrain along a path.
     */
    public TerrainAnalysis analyzePathTerrain(List<Node> path) {
        if (path.isEmpty()) {
            return new TerrainAnalysis(0, 0, 0, 0, 0, new HashMap<>());
        }
        
        double totalElevation = 0;
        double elevationChanges = 0;
        int hazardCount = 0;
        int waterCount = 0;
        int obstacleCount = 0;
        Map<ObstacleDetector.BlockType, Integer> blockDistribution = new HashMap<>();
        
        Node previousNode = null;
        for (Node node : path) {
            totalElevation += node.getY();
            
            if (previousNode != null) {
                elevationChanges += Math.abs(node.getY() - previousNode.getY());
            }
            
            // Detect obstacles and hazards
            ObstacleDetector.Obstacle obstacle = obstacleDetector.detectObstacle(
                node.getX(), node.getY(), node.getZ());
            
            if (obstacle.getDangerLevel() > 5) {
                hazardCount++;
            }
            
            if (obstacle.getType() == ObstacleDetector.ObstacleType.LIQUID) {
                waterCount++;
            }
            
            if (obstacle.getType() != ObstacleDetector.ObstacleType.NONE) {
                obstacleCount++;
            }
            
            // Track block distribution
            ObstacleDetector.BlockType blockType = obstacleDetector.world.getBlockAt(
                node.getX(), node.getY() - 1, node.getZ());
            blockDistribution.merge(blockType, 1, Integer::sum);
            
            previousNode = node;
        }
        
        double averageElevation = totalElevation / path.size();
        double terrainRoughness = elevationChanges / Math.max(1, path.size() - 1);
        double hazardDensity = (double) hazardCount / path.size();
        double waterCoverage = (double) waterCount / path.size();
        
        return new TerrainAnalysis(averageElevation, terrainRoughness,
                                  hazardDensity, waterCoverage, obstacleCount,
                                  blockDistribution);
    }
    
    /**
     * Analyzes a region of terrain.
     */
    public TerrainAnalysis analyzeRegion(int centerX, int centerY, int centerZ, int radius) {
        List<Node> samplePoints = new ArrayList<>();
        
        for (int x = centerX - radius; x <= centerX + radius; x += 2) {
            for (int z = centerZ - radius; z <= centerZ + radius; z += 2) {
                // Find ground level
                for (int y = centerY + radius; y >= centerY - radius; y--) {
                    if (obstacleDetector.isWalkable(x, y, z)) {
                        samplePoints.add(new Node(x, y, z));
                        break;
                    }
                }
            }
        }
        
        return analyzePathTerrain(samplePoints);
    }
    
    /**
     * Configures the pathfinding engine for a specific strategy.
     */
    private void configureEngineForStrategy(PathStrategy strategy) {
        // Strategy-specific configuration would be implemented here
        // This is a simplified version
        switch (strategy) {
            case SHORTEST:
                // Configure for shortest path
                break;
            case SAFEST:
                // Configure to avoid hazards
                break;
            case FASTEST:
                // Configure for speed
                break;
            case BALANCED:
                // Use default configuration
                break;
            case EXPLORATION:
                // Configure for exploration
                break;
        }
    }
    
    /**
     * Calculates total distance of a path.
     */
    private double calculateTotalDistance(List<Node> path) {
        double totalDistance = 0;
        for (int i = 1; i < path.size(); i++) {
            totalDistance += path.get(i - 1).getDistanceTo(path.get(i));
        }
        return totalDistance;
    }
    
    /**
     * Estimates traversal time for a path.
     */
    private double estimateTraversalTime(List<Node> path) {
        double totalTime = 0;
        
        for (int i = 1; i < path.size(); i++) {
            Node from = path.get(i - 1);
            Node to = path.get(i);
            
            double distance = from.getDistanceTo(to);
            double terrainFactor = to.getTerrainCost();
            
            // Base movement speed (blocks per second)
            double baseSpeed = 4.3;
            
            // Adjust for movement type
            ObstacleDetector.MovementType moveType = 
                obstacleDetector.getRequiredMovement(from, to);
            
            double speedMultiplier = 1.0;
            switch (moveType) {
                case WALK:
                    speedMultiplier = 1.0;
                    break;
                case JUMP:
                    speedMultiplier = 0.8;
                    break;
                case FALL:
                    speedMultiplier = 2.0;
                    break;
                case SWIM:
                    speedMultiplier = 0.5;
                    break;
                case CLIMB:
                    speedMultiplier = 0.3;
                    break;
            }
            
            double effectiveSpeed = baseSpeed * speedMultiplier / terrainFactor;
            totalTime += distance / effectiveSpeed;
        }
        
        return totalTime;
    }
    
    /**
     * Assesses the danger level of a path.
     */
    private double assessPathDanger(List<Node> path) {
        double totalDanger = 0;
        
        for (Node node : path) {
            ObstacleDetector.Obstacle obstacle = obstacleDetector.detectObstacle(
                node.getX(), node.getY(), node.getZ());
            totalDanger += obstacle.getDangerLevel();
        }
        
        return totalDanger / Math.max(1, path.size());
    }
    
    /**
     * Generates movement sequence for a path.
     */
    private List<ObstacleDetector.MovementType> generateMovementSequence(List<Node> path) {
        List<ObstacleDetector.MovementType> sequence = new ArrayList<>();
        
        for (int i = 1; i < path.size(); i++) {
            ObstacleDetector.MovementType moveType = 
                obstacleDetector.getRequiredMovement(path.get(i - 1), path.get(i));
            sequence.add(moveType);
        }
        
        return sequence;
    }
    
    /**
     * Generates waypoints to create alternative paths.
     */
    private List<Node> generateWaypoints(Node start, Node goal, Set<Node> avoidArea) {
        List<Node> waypoints = new ArrayList<>();
        
        // Calculate bounding box
        int minX = Math.min(start.getX(), goal.getX()) - 10;
        int maxX = Math.max(start.getX(), goal.getX()) + 10;
        int minZ = Math.min(start.getZ(), goal.getZ()) - 10;
        int maxZ = Math.max(start.getZ(), goal.getZ()) + 10;
        int y = (start.getY() + goal.getY()) / 2;
        
        // Generate grid of potential waypoints
        for (int x = minX; x <= maxX; x += 5) {
            for (int z = minZ; z <= maxZ; z += 5) {
                Node waypoint = new Node(x, y, z);
                
                // Skip if in avoid area
                if (avoidArea.contains(waypoint)) {
                    continue;
                }
                
                // Check if walkable
                if (obstacleDetector.isWalkable(x, y, z)) {
                    waypoints.add(waypoint);
                }
            }
        }
        
        // Sort by distance from line between start and goal
        waypoints.sort(Comparator.comparingDouble(w -> 
            getDistanceFromLine(w, start, goal)));
        
        // Return top waypoints
        return waypoints.stream()
                       .limit(10)
                       .collect(Collectors.toList());
    }
    
    /**
     * Calculates distance from a point to a line.
     */
    private double getDistanceFromLine(Node point, Node lineStart, Node lineEnd) {
        double A = point.getDistanceTo(lineStart);
        double B = point.getDistanceTo(lineEnd);
        double C = lineStart.getDistanceTo(lineEnd);
        
        if (C == 0) return A;
        
        double s = (A + B + C) / 2;
        double area = Math.sqrt(s * (s - A) * (s - B) * (s - C));
        
        return 2 * area / C;
    }
    
    /**
     * Validates a path for traversability.
     */
    public boolean validatePath(List<Node> path) {
        if (path.isEmpty()) {
            return false;
        }
        
        for (int i = 0; i < path.size(); i++) {
            Node node = path.get(i);
            
            // Check if position is walkable
            if (!obstacleDetector.isWalkable(node.getX(), node.getY(), node.getZ())) {
                return false;
            }
            
            // Check if movement between nodes is valid
            if (i > 0) {
                Node previous = path.get(i - 1);
                if (!obstacleDetector.isPathClear(previous, node)) {
                    return false;
                }
            }
        }
        
        return true;
    }
}
