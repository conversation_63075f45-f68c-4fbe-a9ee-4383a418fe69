@echo off
echo ========================================
echo    Compilacao Completa do JasperGoTo
echo ========================================
echo.

REM Definir Java 8 JDK se disponível
for /d %%i in ("C:\Program Files\Java\jdk1.8*") do set "JAVA_HOME=%%i"
for /d %%i in ("C:\Program Files (x86)\Java\jdk1.8*") do set "JAVA_HOME=%%i"

if defined JAVA_HOME (
    echo Usando Java 8 JDK: %JAVA_HOME%
    set "PATH=%JAVA_HOME%\bin;%PATH%"
) else (
    echo Usando Java atual...
)

echo.
echo Tentando compilacao com Gradle usando JDK...

REM Definir explicitamente o JDK para o Gradle
set "GRADLE_OPTS=-Dorg.gradle.java.home=%JAVA_HOME%"

echo Executando: gradlew build --no-daemon --refresh-dependencies
gradlew build --no-daemon --refresh-dependencies

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo    SUCESSO! JAR CRIADO
    echo ========================================
    echo.
    dir build\libs\*.jar
    echo.
    echo Copie o arquivo .jar para a pasta mods do Minecraft!
) else (
    echo.
    echo ========================================
    echo    FALHA NA COMPILACAO
    echo ========================================
    echo.
    echo Problema: Falta JDK 8 ou dependencias
    echo Solucao: Instale Java 8 JDK completo
)

echo.
pause