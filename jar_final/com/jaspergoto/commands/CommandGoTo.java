package com.jaspergoto.commands;

// Versão stub para compilação
public class CommandGoTo {
    
    public String getCommandName() {
        return "jaspergoto";
    }
    
    public String getCommandUsage(Object sender) {
        return "/jaspergoto <x> <y> <z> - Vai automaticamente até as coordenadas\n" +
               "/jaspergoto stop - Para o movimento\n" +
               "/jaspergoto clear - Limpa o caminho";
    }
    
    public void processCommand(Object sender, String[] args) {
        System.out.println("[JasperGoTo] Comando executado: " + String.join(" ", args));
        
        if (args.length == 0) {
            System.out.println("[JasperGoTo] Uso: /jaspergoto <x> <y> <z>");
            return;
        }
        
        if (args[0].equalsIgnoreCase("stop")) {
            System.out.println("[JasperGoTo] Movimento parado!");
            return;
        }
        
        if (args[0].equalsIgnoreCase("clear")) {
            System.out.println("[JasperGoTo] Caminho limpo!");
            return;
        }
        
        if (args.length >= 3) {
            try {
                int x = Integer.parseInt(args[0]);
                int y = Integer.parseInt(args[1]);
                int z = Integer.parseInt(args[2]);
                
                System.out.println("[JasperGoTo] Calculando rota para " + x + ", " + y + ", " + z);
                System.out.println("[JasperGoTo] Rota encontrada! Iniciando movimento...");
            } catch (NumberFormatException e) {
                System.out.println("[JasperGoTo] Coordenadas inválidas!");
            }
        }
    }
    
    public int getRequiredPermissionLevel() {
        return 0;
    }
}