@echo off
echo ========================================
echo    Compilando JasperGoTo Manualmente
echo ========================================
echo.

REM Criar diretórios
if not exist "build" mkdir build
if not exist "build\classes" mkdir build\classes
if not exist "build\libs" mkdir build\libs

echo Configurando Java 8...
set "JAVA_HOME=C:\Program Files\Java\jdk1.8.0_333"
if exist "%JAVA_HOME%\bin\javac.exe" (
    set "PATH=%JAVA_HOME%\bin;%PATH%"
    echo Java 8 configurado!
) else (
    echo ERRO: Java 8 JDK nao encontrado!
    echo Por favor, instale o Java 8 Development Kit.
    pause
    exit /b 1
)

echo.
echo Compilando código fonte...

REM Compilar as classes Java
javac -cp "." -d "build\classes" src\main\java\com\jaspergoto\*.java src\main\java\com\jaspergoto\commands\*.java src\main\java\com\jaspergoto\pathfinding\*.java

if %ERRORLEVEL% NEQ 0 (
    echo ERRO: Falha na compilação
    echo.
    echo SOLUÇÃO: Use o IntelliJ para compilar:
    echo 1. Build → Build Project
    echo 2. Ou Build → Build Artifacts
    echo.
    pause
    exit /b 1
)

echo.
echo Criando JAR...

REM Criar o arquivo JAR
cd build\classes
jar cf ..\libs\jaspergoto-1.0.0.jar com\
cd ..\..

echo.
echo ========================================
echo    COMPILAÇÃO CONCLUÍDA!
echo ========================================
echo.
echo Arquivo criado: build\libs\jaspergoto-1.0.0.jar
echo.
echo Para usar no Minecraft:
echo 1. Copie o arquivo .jar para a pasta mods do Minecraft
echo 2. Execute Minecraft 1.8.9 com Forge
echo 3. Use /jaspergoto x y z no jogo
echo.
pause