import java.util.*;

/**
 * Optimizes paths with waypoint smoothing and simplification techniques.
 * Reduces unnecessary waypoints while maintaining path validity.
 */
public class PathOptimizer {
    
    // Optimization parameters
    private double smoothingFactor;
    private int maxIterations;
    private double simplificationTolerance;
    private boolean enableCornerCutting;
    private boolean enableHeightOptimization;
    
    /**
     * Result of path optimization.
     */
    public static class OptimizationResult {
        private final List<Node> optimizedPath;
        private final int waypointsRemoved;
        private final double distanceReduction;
        private final long optimizationTimeMs;
        
        public OptimizationResult(List<Node> optimizedPath, int waypointsRemoved,
                                 double distanceReduction, long optimizationTimeMs) {
            this.optimizedPath = new ArrayList<>(optimizedPath);
            this.waypointsRemoved = waypointsRemoved;
            this.distanceReduction = distanceReduction;
            this.optimizationTimeMs = optimizationTimeMs;
        }
        
        // Getters
        public List<Node> getOptimizedPath() { return new ArrayList<>(optimizedPath); }
        public int getWaypointsRemoved() { return waypointsRemoved; }
        public double getDistanceReduction() { return distanceReduction; }
        public long getOptimizationTimeMs() { return optimizationTimeMs; }
        
        @Override
        public String toString() {
            return String.format("OptimizationResult[removed=%d, reduction=%.1f, time=%dms]",
                    waypointsRemoved, distanceReduction, optimizationTimeMs);
        }
    }
    
    /**
     * Creates a path optimizer with default settings.
     */
    public PathOptimizer() {
        this.smoothingFactor = 0.5;
        this.maxIterations = 10;
        this.simplificationTolerance = 0.5;
        this.enableCornerCutting = true;
        this.enableHeightOptimization = true;
    }
    
    /**
     * Creates a path optimizer with custom settings.
     */
    public PathOptimizer(double smoothingFactor, int maxIterations,
                        double simplificationTolerance, boolean enableCornerCutting,
                        boolean enableHeightOptimization) {
        this.smoothingFactor = Math.max(0, Math.min(1, smoothingFactor));
        this.maxIterations = Math.max(1, maxIterations);
        this.simplificationTolerance = Math.max(0, simplificationTolerance);
        this.enableCornerCutting = enableCornerCutting;
        this.enableHeightOptimization = enableHeightOptimization;
    }
    
    /**
     * Optimizes a path using multiple techniques.
     */
    public List<Node> optimizePath(List<Node> path, ObstacleDetector obstacleDetector) {
        if (path == null || path.size() <= 2) {
            return path;
        }
        
        long startTime = System.currentTimeMillis();
        List<Node> optimized = new ArrayList<>(path);
        
        // Apply optimization techniques in sequence
        optimized = removeRedundantWaypoints(optimized, obstacleDetector);
        optimized = smoothPath(optimized, obstacleDetector);
        optimized = simplifyPath(optimized, obstacleDetector);
        
        if (enableCornerCutting) {
            optimized = cutCorners(optimized, obstacleDetector);
        }
        
        if (enableHeightOptimization) {
            optimized = optimizeHeight(optimized, obstacleDetector);
        }
        
        return optimized;
    }
    
    /**
     * Gets detailed optimization result with statistics.
     */
    public OptimizationResult optimizePathDetailed(List<Node> path, 
                                                   ObstacleDetector obstacleDetector) {
        long startTime = System.currentTimeMillis();
        
        if (path == null || path.size() <= 2) {
            return new OptimizationResult(path, 0, 0, 0);
        }
        
        int originalSize = path.size();
        double originalDistance = calculatePathDistance(path);
        
        List<Node> optimized = optimizePath(path, obstacleDetector);
        
        int waypointsRemoved = originalSize - optimized.size();
        double newDistance = calculatePathDistance(optimized);
        double distanceReduction = originalDistance - newDistance;
        long optimizationTime = System.currentTimeMillis() - startTime;
        
        return new OptimizationResult(optimized, waypointsRemoved, 
                                     distanceReduction, optimizationTime);
    }
    
    /**
     * Removes redundant waypoints using line-of-sight checks.
     */
    private List<Node> removeRedundantWaypoints(List<Node> path, 
                                               ObstacleDetector obstacleDetector) {
        if (path.size() <= 2) {
            return path;
        }
        
        List<Node> optimized = new ArrayList<>();
        optimized.add(path.get(0));
        
        int currentIndex = 0;
        while (currentIndex < path.size() - 1) {
            int furthestVisible = currentIndex + 1;
            
            // Find the furthest node we can reach directly
            for (int i = currentIndex + 2; i < path.size(); i++) {
                if (hasLineOfSight(path.get(currentIndex), path.get(i), obstacleDetector)) {
                    furthestVisible = i;
                } else {
                    break;
                }
            }
            
            optimized.add(path.get(furthestVisible));
            currentIndex = furthestVisible;
        }
        
        return optimized;
    }
    
    /**
     * Smooths the path using weighted averaging.
     */
    private List<Node> smoothPath(List<Node> path, ObstacleDetector obstacleDetector) {
        if (path.size() <= 2) {
            return path;
        }
        
        List<Node> smoothed = new ArrayList<>(path);
        boolean changed = true;
        int iterations = 0;
        
        while (changed && iterations < maxIterations) {
            changed = false;
            List<Node> newPath = new ArrayList<>();
            newPath.add(smoothed.get(0)); // Keep start
            
            for (int i = 1; i < smoothed.size() - 1; i++) {
                Node current = smoothed.get(i);
                Node previous = smoothed.get(i - 1);
                Node next = smoothed.get(i + 1);
                
                // Calculate smoothed position
                double newX = current.getX() * (1 - smoothingFactor) +
                             (previous.getX() + next.getX()) * smoothingFactor / 2;
                double newY = current.getY() * (1 - smoothingFactor) +
                             (previous.getY() + next.getY()) * smoothingFactor / 2;
                double newZ = current.getZ() * (1 - smoothingFactor) +
                             (previous.getZ() + next.getZ()) * smoothingFactor / 2;
                
                Node smoothedNode = new Node((int)Math.round(newX), 
                                            (int)Math.round(newY), 
                                            (int)Math.round(newZ));
                
                // Check if smoothed position is valid
                if (obstacleDetector.isWalkable(smoothedNode.getX(), 
                                               smoothedNode.getY(), 
                                               smoothedNode.getZ()) &&
                    obstacleDetector.isPathClear(previous, smoothedNode) &&
                    obstacleDetector.isPathClear(smoothedNode, next)) {
                    newPath.add(smoothedNode);
                    if (!smoothedNode.equals(current)) {
                        changed = true;
                    }
                } else {
                    newPath.add(current);
                }
            }
            
            newPath.add(smoothed.get(smoothed.size() - 1)); // Keep end
            smoothed = newPath;
            iterations++;
        }
        
        return smoothed;
    }
    
    /**
     * Simplifies the path using Douglas-Peucker algorithm.
     */
    private List<Node> simplifyPath(List<Node> path, ObstacleDetector obstacleDetector) {
        if (path.size() <= 2) {
            return path;
        }
        
        List<Node> simplified = douglasPeucker(path, 0, path.size() - 1, 
                                               simplificationTolerance);
        
        // Validate simplified path
        if (!validatePath(simplified, obstacleDetector)) {
            return path; // Return original if simplification breaks the path
        }
        
        return simplified;
    }
    
    /**
     * Douglas-Peucker recursive simplification.
     */
    private List<Node> douglasPeucker(List<Node> path, int startIdx, int endIdx, 
                                      double epsilon) {
        if (endIdx - startIdx <= 1) {
            List<Node> result = new ArrayList<>();
            result.add(path.get(startIdx));
            if (startIdx != endIdx) {
                result.add(path.get(endIdx));
            }
            return result;
        }
        
        // Find point with maximum distance from line
        double maxDist = 0;
        int maxIdx = startIdx;
        
        for (int i = startIdx + 1; i < endIdx; i++) {
            double dist = perpendicularDistance(path.get(i), 
                                               path.get(startIdx), 
                                               path.get(endIdx));
            if (dist > maxDist) {
                maxDist = dist;
                maxIdx = i;
            }
        }
        
        // If max distance is greater than epsilon, recursively simplify
        if (maxDist > epsilon) {
            List<Node> left = douglasPeucker(path, startIdx, maxIdx, epsilon);
            List<Node> right = douglasPeucker(path, maxIdx, endIdx, epsilon);
            
            // Combine results
            List<Node> result = new ArrayList<>(left);
            result.addAll(right.subList(1, right.size()));
            return result;
        } else {
            // Return just the endpoints
            List<Node> result = new ArrayList<>();
            result.add(path.get(startIdx));
            result.add(path.get(endIdx));
            return result;
        }
    }
    
    /**
     * Cuts corners where possible to create smoother paths.
     */
    private List<Node> cutCorners(List<Node> path, ObstacleDetector obstacleDetector) {
        if (path.size() <= 2) {
            return path;
        }
        
        List<Node> optimized = new ArrayList<>();
        optimized.add(path.get(0));
        
        for (int i = 1; i < path.size() - 1; i++) {
            Node previous = optimized.get(optimized.size() - 1);
            Node current = path.get(i);
            Node next = path.get(i + 1);
            
            // Check if we can cut the corner
            Node cornerCut = findCornerCut(previous, current, next, obstacleDetector);
            if (cornerCut != null && !cornerCut.equals(current)) {
                optimized.add(cornerCut);
            } else {
                optimized.add(current);
            }
        }
        
        optimized.add(path.get(path.size() - 1));
        return optimized;
    }
    
    /**
     * Finds an optimal corner cut position.
     */
    private Node findCornerCut(Node previous, Node current, Node next, 
                               ObstacleDetector obstacleDetector) {
        // Calculate the midpoint between previous and next
        int midX = (previous.getX() + next.getX()) / 2;
        int midY = (previous.getY() + next.getY()) / 2;
        int midZ = (previous.getZ() + next.getZ()) / 2;
        
        Node midpoint = new Node(midX, midY, midZ);
        
        // Check if midpoint is valid and reachable
        if (obstacleDetector.isWalkable(midX, midY, midZ) &&
            obstacleDetector.isPathClear(previous, midpoint) &&
            obstacleDetector.isPathClear(midpoint, next)) {
            return midpoint;
        }
        
        // Try positions between current and midpoint
        for (double t = 0.25; t <= 0.75; t += 0.25) {
            int x = (int)Math.round(current.getX() * (1 - t) + midX * t);
            int y = (int)Math.round(current.getY() * (1 - t) + midY * t);
            int z = (int)Math.round(current.getZ() * (1 - t) + midZ * t);
            
            Node candidate = new Node(x, y, z);
            if (obstacleDetector.isWalkable(x, y, z) &&
                obstacleDetector.isPathClear(previous, candidate) &&
                obstacleDetector.isPathClear(candidate, next)) {
                return candidate;
            }
        }
        
        return null;
    }
    
    /**
     * Optimizes vertical movement to minimize unnecessary height changes.
     */
    private List<Node> optimizeHeight(List<Node> path, ObstacleDetector obstacleDetector) {
        if (path.size() <= 2) {
            return path;
        }
        
        List<Node> optimized = new ArrayList<>();
        optimized.add(path.get(0));
        
        for (int i = 1; i < path.size() - 1; i++) {
            Node current = path.get(i);
            Node previous = optimized.get(optimized.size() - 1);
            Node next = path.get(i + 1);
            
            // Try to maintain consistent height where possible
            int optimalY = findOptimalHeight(previous, current, next, obstacleDetector);
            
            if (optimalY != current.getY()) {
                Node optimizedNode = new Node(current.getX(), optimalY, current.getZ());
                if (obstacleDetector.isWalkable(optimizedNode.getX(), 
                                               optimizedNode.getY(), 
                                               optimizedNode.getZ()) &&
                    obstacleDetector.isPathClear(previous, optimizedNode) &&
                    obstacleDetector.isPathClear(optimizedNode, next)) {
                    optimized.add(optimizedNode);
                } else {
                    optimized.add(current);
                }
            } else {
                optimized.add(current);
            }
        }
        
        optimized.add(path.get(path.size() - 1));
        return optimized;
    }
    
    /**
     * Finds the optimal height for a waypoint.
     */
    private int findOptimalHeight(Node previous, Node current, Node next,
                                  ObstacleDetector obstacleDetector) {
        // Prefer to maintain the average height of neighbors
        int averageY = (previous.getY() + next.getY()) / 2;
        
        // Check heights near the average
        for (int dy = 0; dy <= 2; dy++) {
            for (int sign : new int[]{1, -1}) {
                if (dy == 0 && sign == -1) continue;
                
                int testY = averageY + sign * dy;
                if (Math.abs(testY - previous.getY()) <= 3 && 
                    Math.abs(testY - next.getY()) <= 3) {
                    if (obstacleDetector.isWalkable(current.getX(), testY, current.getZ())) {
                        return testY;
                    }
                }
            }
        }
        
        return current.getY();
    }
    
    /**
     * Checks if there's line of sight between two nodes.
     */
    private boolean hasLineOfSight(Node start, Node end, ObstacleDetector obstacleDetector) {
        // Use Bresenham's 3D algorithm to check all points along the line
        int x0 = start.getX(), y0 = start.getY(), z0 = start.getZ();
        int x1 = end.getX(), y1 = end.getY(), z1 = end.getZ();
        
        int dx = Math.abs(x1 - x0);
        int dy = Math.abs(y1 - y0);
        int dz = Math.abs(z1 - z0);
        
        int xs = x1 > x0 ? 1 : -1;
        int ys = y1 > y0 ? 1 : -1;
        int zs = z1 > z0 ? 1 : -1;
        
        // Driving axis is the axis with the greatest delta
        if (dx >= dy && dx >= dz) {
            // X is driving axis
            int p1 = 2 * dy - dx;
            int p2 = 2 * dz - dx;
            while (x0 != x1) {
                x0 += xs;
                if (p1 >= 0) {
                    y0 += ys;
                    p1 -= 2 * dx;
                }
                if (p2 >= 0) {
                    z0 += zs;
                    p2 -= 2 * dx;
                }
                p1 += 2 * dy;
                p2 += 2 * dz;
                
                if (!obstacleDetector.isWalkable(x0, y0, z0)) {
                    return false;
                }
            }
        } else if (dy >= dx && dy >= dz) {
            // Y is driving axis
            int p1 = 2 * dx - dy;
            int p2 = 2 * dz - dy;
            while (y0 != y1) {
                y0 += ys;
                if (p1 >= 0) {
                    x0 += xs;
                    p1 -= 2 * dy;
                }
                if (p2 >= 0) {
                    z0 += zs;
                    p2 -= 2 * dy;
                }
                p1 += 2 * dx;
                p2 += 2 * dz;
                
                if (!obstacleDetector.isWalkable(x0, y0, z0)) {
                    return false;
                }
            }
        } else {
            // Z is driving axis
            int p1 = 2 * dy - dz;
            int p2 = 2 * dx - dz;
            while (z0 != z1) {
                z0 += zs;
                if (p1 >= 0) {
                    y0 += ys;
                    p1 -= 2 * dz;
                }
                if (p2 >= 0) {
                    x0 += xs;
                    p2 -= 2 * dz;
                }
                p1 += 2 * dy;
                p2 += 2 * dx;
                
                if (!obstacleDetector.isWalkable(x0, y0, z0)) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * Calculates perpendicular distance from point to line.
     */
    private double perpendicularDistance(Node point, Node lineStart, Node lineEnd) {
        double A = point.getDistanceTo(lineStart);
        double B = point.getDistanceTo(lineEnd);
        double C = lineStart.getDistanceTo(lineEnd);
        
        if (C == 0) return A;
        
        // Using Heron's formula
        double s = (A + B + C) / 2;
        double area = Math.sqrt(Math.max(0, s * (s - A) * (s - B) * (s - C)));
        
        return 2 * area / C;
    }
    
    /**
     * Calculates total path distance.
     */
    private double calculatePathDistance(List<Node> path) {
        if (path == null || path.size() < 2) {
            return 0;
        }
        
        double totalDistance = 0;
        for (int i = 1; i < path.size(); i++) {
            totalDistance += path.get(i - 1).getDistanceTo(path.get(i));
        }
        return totalDistance;
    }
    
    /**
     * Validates that a path is traversable.
     */
    private boolean validatePath(List<Node> path, ObstacleDetector obstacleDetector) {
        if (path == null || path.isEmpty()) {
            return false;
        }
        
        for (int i = 0; i < path.size(); i++) {
            Node node = path.get(i);
            
            // Check if position is walkable
            if (!obstacleDetector.isWalkable(node.getX(), node.getY(), node.getZ())) {
                return false;
            }
            
            // Check path between consecutive nodes
            if (i > 0) {
                if (!obstacleDetector.isPathClear(path.get(i - 1), node)) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    // Configuration getters and setters
    public double getSmoothingFactor() { return smoothingFactor; }
    public void setSmoothingFactor(double smoothingFactor) { 
        this.smoothingFactor = Math.max(0, Math.min(1, smoothingFactor)); 
    }
    
    public int getMaxIterations() { return maxIterations; }
    public void setMaxIterations(int maxIterations) { 
        this.maxIterations = Math.max(1, maxIterations); 
    }
    
    public double getSimplificationTolerance() { return simplificationTolerance; }
    public void setSimplificationTolerance(double simplificationTolerance) { 
        this.simplificationTolerance = Math.max(0, simplificationTolerance); 
    }
    
    public boolean isEnableCornerCutting() { return enableCornerCutting; }
    public void setEnableCornerCutting(boolean enableCornerCutting) { 
        this.enableCornerCutting = enableCornerCutting; 
    }
    
    public boolean isEnableHeightOptimization() { return enableHeightOptimization; }
    public void setEnableHeightOptimization(boolean enableHeightOptimization) { 
        this.enableHeightOptimization = enableHeightOptimization; 
    }
}
