@echo off
echo ========================================
echo   JasperGoTo - Cliente de Teste
echo ========================================
echo.

REM Verifica se existe a pasta run
if not exist "run" (
    echo Criando pasta run...
    mkdir run
)

REM Tenta usar Java 8 se estiver disponível
set JAVA8_HOME=C:\Program Files\Java\jre1.8.0_351
if exist "%JAVA8_HOME%\bin\java.exe" (
    echo Usando Java 8 encontrado...
    set JAVA_HOME=%JAVA8_HOME%
    set PATH=%JAVA8_HOME%\bin;%PATH%
) else (
    echo AVISO: Java 8 nao encontrado. Tentando com Java atual...
    echo Para melhor compatibilidade, instale Java 8.
    echo.
)

echo Iniciando cliente de desenvolvimento...
echo.
echo Controles do Mod:
echo - Tecla G: Ativar/Desativar Auto-Walk
echo - Tecla H: Ativar/Desativar Auto-Sprint
echo.

REM Executa o cliente de teste
gradlew.bat runClient

pause
