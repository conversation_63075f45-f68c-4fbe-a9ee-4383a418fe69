package com.jaspergoto.pathfinding;

import net.minecraft.client.Minecraft;
import net.minecraft.util.BlockPos;
import net.minecraft.block.Block;
import net.minecraft.block.material.Material;
import net.minecraft.world.World;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.Vec3;

import java.util.*;
import java.util.concurrent.*;

public class AdvancedPathfindingEngine {
    
    // === CONFIGURAÇÕES DE PERFORMANCE ===
    private static final int MAX_PATH_LENGTH = 8000;
    private static final int MAX_ITERATIONS = 75000;
    private static final int SEARCH_RADIUS = 350;
    private static final double HEURISTIC_WEIGHT = 1.4;
    private static final int MAX_RECALC_ATTEMPTS = 5;
    private static final long RECALC_COOLDOWN = 1500;
    private static final int CHUNK_PRELOAD_RADIUS = 3;
    
    // === CONFIGURAÇÕES DE MOVIMENTO ===
    private static final double DIAGONAL_COST = 1.414;
    private static final double JUMP_COST = 1.5;
    private static final double FALL_COST = 0.8;
    private static final double SPRINT_JUMP_DISTANCE = 4.5;
    private static final int MAX_SAFE_FALL = 4;
    private static final int MAX_JUMP_HEIGHT = 2;
    
    // === CACHE E OTIMIZAÇÃO ===
    private final Map<BlockPos, Boolean> walkableCache = new ConcurrentHashMap<>();
    private final Map<String, List<BlockPos>> pathCache = new ConcurrentHashMap<>();
    private final Set<BlockPos> dangerousBlocks = ConcurrentHashMap.newKeySet();
    private final ExecutorService pathfindingExecutor = Executors.newSingleThreadExecutor(r -> {
        Thread t = new Thread(r, "PathfindingThread");
        t.setDaemon(true);
        return t;
    });
    
    // === SISTEMA ANTI-TRAVAMENTO ===
    private volatile boolean isCalculating = false;
    private volatile boolean shouldStop = false;
    private int recalcAttempts = 0;
    private long lastRecalcTime = 0;
    private long lastCacheClean = 0;
    private static final long CACHE_CLEAN_INTERVAL = 30000; // 30 segundos
    
    // === ESTADO DO PATHFINDING ===
    private World world;
    private List<BlockPos> currentPath = Collections.synchronizedList(new ArrayList<>());
    private BlockPos targetPos;
    private BlockPos lastPlayerPos;
    private CompletableFuture<List<BlockPos>> currentCalculation;
    
    // === MÉTRICAS DE PERFORMANCE ===
    private long totalCalculationTime = 0;
    private int pathsCalculated = 0;
    private int cacheHits = 0;
    private int cacheMisses = 0;
    
    public AdvancedPathfindingEngine(World world) {
        this.world = world;
        startMaintenanceTask();
    }
    
    // === CLASSE NODE OTIMIZADA ===
    public static class Node implements Comparable<Node> {
        final BlockPos pos;
        final Node parent;
        final double g, h, f;
        final int depth;
        final MovementType movementType;
        
        public Node(BlockPos pos, Node parent, double g, double h, MovementType movementType) {
            this.pos = pos;
            this.parent = parent;
            this.g = g;
            this.h = h;
            this.f = g + (h * HEURISTIC_WEIGHT);
            this.depth = parent == null ? 0 : parent.depth + 1;
            this.movementType = movementType;
        }
        
        @Override
        public int compareTo(Node other) {
            int result = Double.compare(this.f, other.f);
            if (result == 0) {
                result = Double.compare(this.h, other.h); // Tie-breaker
            }
            return result;
        }
        
        @Override
        public boolean equals(Object obj) {
            return obj instanceof Node && ((Node) obj).pos.equals(this.pos);
        }
        
        @Override
        public int hashCode() {
            return pos.hashCode();
        }
    }
    
    // === TIPOS DE MOVIMENTO ===
    public enum MovementType {
        WALK(1.0), DIAGONAL(DIAGONAL_COST), JUMP(JUMP_COST), 
        FALL(FALL_COST), SPRINT_JUMP(2.0), SWIM(2.5);
        
        public final double cost;
        MovementType(double cost) { this.cost = cost; }
    }
    
    // === PATHFINDING ASSÍNCRONO ===
    public CompletableFuture<List<BlockPos>> findPathAsync(BlockPos start, BlockPos target) {
        String cacheKey = start + "->" + target;
        
        // Verificar cache primeiro
        List<BlockPos> cachedPath = pathCache.get(cacheKey);
        if (cachedPath != null && isPathStillValid(cachedPath)) {
            cacheHits++;
            return CompletableFuture.completedFuture(new ArrayList<>(cachedPath));
        }
        cacheMisses++;
        
        // Cancelar cálculo anterior se estiver rodando
        if (currentCalculation != null && !currentCalculation.isDone()) {
            currentCalculation.cancel(true);
            shouldStop = true;
        }
        
        shouldStop = false;
        currentCalculation = CompletableFuture.supplyAsync(() -> {
            try {
                return calculatePath(start, target);
            } catch (Exception e) {
                System.err.println("[JasperGoTo] Erro no cálculo do caminho: " + e.getMessage());
                return createEmergencyPath(start, target);
            }
        }, pathfindingExecutor);
        
        return currentCalculation;
    }
    
    // === PATHFINDING SÍNCRONO (PARA COMPATIBILIDADE) ===
    public List<BlockPos> findPath(BlockPos start, BlockPos target) {
        try {
            return findPathAsync(start, target).get(5, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            System.out.println("[JasperGoTo] Timeout no pathfinding, usando caminho de emergência");
            return createEmergencyPath(start, target);
        } catch (Exception e) {
            System.err.println("[JasperGoTo] Erro: " + e.getMessage());
            return createEmergencyPath(start, target);
        }
    }
    
    // === ALGORITMO A* OTIMIZADO ===
    private List<BlockPos> calculatePath(BlockPos start, BlockPos target) {
        long startTime = System.currentTimeMillis();
        isCalculating = true;
        
        try {
            // Pré-carregar chunks necessários
            preloadChunks(start, target);
            
            // Jump Point Search para performance em áreas abertas
            if (isOpenArea(start, target)) {
                List<BlockPos> jpsPath = jumpPointSearch(start, target);
                if (jpsPath != null && !jpsPath.isEmpty()) {
                    return cachePath(start, target, jpsPath);
                }
            }
            
            // A* tradicional para áreas complexas
            return performAStar(start, target);
            
        } finally {
            isCalculating = false;
            totalCalculationTime += System.currentTimeMillis() - startTime;
            pathsCalculated++;
        }
    }
    
    private List<BlockPos> performAStar(BlockPos start, BlockPos target) {
        PriorityQueue<Node> openSet = new PriorityQueue<>();
        Set<BlockPos> closedSet = ConcurrentHashMap.newKeySet();
        Map<BlockPos, Double> bestG = new ConcurrentHashMap<>();
        
        Node startNode = new Node(start, null, 0, getAdvancedHeuristic(start, target), MovementType.WALK);
        openSet.add(startNode);
        bestG.put(start, 0.0);
        
        int iterations = 0;
        Node bestNodeSoFar = startNode;
        double bestDistanceSoFar = getDistance(start, target);
        
        while (!openSet.isEmpty() && iterations < MAX_ITERATIONS && !shouldStop) {
            iterations++;
            
            Node current = openSet.poll();
            
            // Verificar se chegou ao destino
            if (isAtTarget(current.pos, target)) {
                List<BlockPos> path = reconstructPath(current);
                return cachePath(start, target, optimizePath(path));
            }
            
            closedSet.add(current.pos);
            
            // Atualizar melhor nó encontrado até agora
            double currentDistance = getDistance(current.pos, target);
            if (currentDistance < bestDistanceSoFar) {
                bestDistanceSoFar = currentDistance;
                bestNodeSoFar = current;
            }
            
            // Expandir vizinhos com movimentos inteligentes
            for (NeighborInfo neighbor : getAdvancedNeighbors(current.pos)) {
                if (closedSet.contains(neighbor.pos) || shouldStop) continue;
                
                double tentativeG = current.g + neighbor.cost;
                
                // Pruning agressivo para performance
                if (tentativeG > MAX_PATH_LENGTH) continue;
                if (bestG.containsKey(neighbor.pos) && tentativeG >= bestG.get(neighbor.pos) + 0.1) continue;
                
                bestG.put(neighbor.pos, tentativeG);
                
                Node neighborNode = new Node(
                    neighbor.pos, current, tentativeG,
                    getAdvancedHeuristic(neighbor.pos, target),
                    neighbor.movementType
                );
                
                openSet.add(neighborNode);
            }
            
            // Limpeza periódica para economizar memória
            if (iterations % 5000 == 0) {
                System.gc(); // Sugestão para garbage collector
            }
        }
        
        // Retornar melhor caminho parcial encontrado
        if (bestNodeSoFar != null) {
            List<BlockPos> partialPath = reconstructPath(bestNodeSoFar);
            System.out.println("[JasperGoTo] Caminho parcial - distância restante: " + 
                             String.format("%.1f", bestDistanceSoFar));
            return optimizePath(partialPath);
        }
        
        return createEmergencyPath(start, target);
    }
    
    // === JUMP POINT SEARCH PARA ÁREAS ABERTAS ===
    private List<BlockPos> jumpPointSearch(BlockPos start, BlockPos target) {
        // Implementação simplificada do JPS para terrenos abertos
        if (hasDirectPath(start, target)) {
            List<BlockPos> directPath = new ArrayList<>();
            directPath.add(start);
            
            // Adicionar pontos intermediários a cada 10 blocos
            double distance = getDistance(start, target);
            int waypoints = Math.max(1, (int)(distance / 10));
            
            for (int i = 1; i < waypoints; i++) {
                double progress = (double) i / waypoints;
                BlockPos waypoint = interpolatePosition(start, target, progress);
                waypoint = findNearestSafeGround(waypoint);
                if (waypoint != null) {
                    directPath.add(waypoint);
                }
            }
            
            directPath.add(target);
            return directPath;
        }
        
        return null; // JPS não aplicável, usar A*
    }
    
    // === SISTEMA DE VIZINHOS AVANÇADO ===
    private static class NeighborInfo {
        final BlockPos pos;
        final double cost;
        final MovementType movementType;
        
        NeighborInfo(BlockPos pos, double cost, MovementType movementType) {
            this.pos = pos;
            this.cost = cost;
            this.movementType = movementType;
        }
    }
    
    private List<NeighborInfo> getAdvancedNeighbors(BlockPos pos) {
        List<NeighborInfo> neighbors = new ArrayList<>();
        
        // Movimentos cardeais e diagonais
        int[] dx = {-1, -1, -1, 0, 0, 1, 1, 1};
        int[] dz = {-1, 0, 1, -1, 1, -1, 0, 1};
        
        for (int i = 0; i < 8; i++) {
            boolean isDiagonal = (i % 2 == 0);
            MovementType baseMovement = isDiagonal ? MovementType.DIAGONAL : MovementType.WALK;
            
            // Movimento horizontal
            addMovementIfValid(neighbors, pos, dx[i], 0, dz[i], baseMovement);
            
            // Pulos (1 e 2 blocos de altura)
            for (int jumpHeight = 1; jumpHeight <= MAX_JUMP_HEIGHT; jumpHeight++) {
                MovementType jumpType = (jumpHeight == 2 || isDiagonal) ? 
                    MovementType.SPRINT_JUMP : MovementType.JUMP;
                addMovementIfValid(neighbors, pos, dx[i], jumpHeight, dz[i], jumpType);
            }
            
            // Quedas seguras (1 a 4 blocos)
            for (int fallHeight = 1; fallHeight <= MAX_SAFE_FALL; fallHeight++) {
                addMovementIfValid(neighbors, pos, dx[i], -fallHeight, dz[i], MovementType.FALL);
            }
        }
        
        return neighbors;
    }
    
    private void addMovementIfValid(List<NeighborInfo> neighbors, BlockPos from, 
                                  int dx, int dy, int dz, MovementType movement) {
        BlockPos to = from.add(dx, dy, dz);
        
        if (isValidMovement(from, to, movement)) {
            double cost = movement.cost * getTerrainModifier(to);
            neighbors.add(new NeighborInfo(to, cost, movement));
        }
    }
    
    // === VALIDAÇÃO DE MOVIMENTO INTELIGENTE ===
    private boolean isValidMovement(BlockPos from, BlockPos to, MovementType movement) {
        // Cache check primeiro
        String cacheKey = from + "->" + to + ":" + movement;
        Boolean cached = walkableCache.get(to);
        if (cached != null) return cached;
        
        boolean valid = false;
        
        switch (movement) {
            case WALK:
            case DIAGONAL:
                valid = isWalkableAdvanced(to) && !hasObstacle(from, to);
                break;
                
            case JUMP:
            case SPRINT_JUMP:
                valid = isWalkableAdvanced(to) && canJumpToAdvanced(from, to) && 
                       hasJumpClearance(from, to);
                break;
                
            case FALL:
                valid = isWalkableAdvanced(to) && canFallSafely(from, to);
                break;
                
            case SWIM:
                valid = isSwimmable(to) && isSwimmable(from);
                break;
        }
        
        // Cache resultado
        walkableCache.put(to, valid);
        return valid;
    }
    
    // === HEURÍSTICA AVANÇADA ===
    private double getAdvancedHeuristic(BlockPos from, BlockPos to) {
        double dx = Math.abs(to.getX() - from.getX());
        double dy = Math.abs(to.getY() - from.getY());
        double dz = Math.abs(to.getZ() - from.getZ());
        
        // Distância Euclidiana com penalizações inteligentes
        double horizontalDistance = Math.sqrt(dx * dx + dz * dz);
        
        // Penalizar mudanças de altura baseado no contexto
        double heightPenalty = dy * (dy > 0 ? 2.0 : 0.5); // Subir é mais custoso que descer
        
        // Penalizar distâncias muito grandes
        double distancePenalty = horizontalDistance > 50 ? horizontalDistance * 0.1 : 0;
        
        // Bônus para estar próximo do destino
        double proximityBonus = horizontalDistance < 10 ? -horizontalDistance * 0.2 : 0;
        
        return horizontalDistance + heightPenalty + distancePenalty + proximityBonus;
    }
    
    // === OTIMIZAÇÃO DE CAMINHO AVANÇADA ===
    private List<BlockPos> optimizePath(List<BlockPos> path) {
        if (path.size() <= 2) return path;
        
        List<BlockPos> optimized = new ArrayList<>();
        optimized.add(path.get(0));
        
        int i = 0;
        while (i < path.size() - 1) {
            int farthestReachable = findFarthestReachablePoint(path, i);
            
            // Suavizar curvas acentuadas
            if (farthestReachable > i + 3) {
                int midPoint = (i + farthestReachable) / 2;
                if (shouldAddWaypoint(path.get(i), path.get(midPoint), path.get(farthestReachable))) {
                    optimized.add(path.get(midPoint));
                    i = midPoint;
                    continue;
                }
            }
            
            optimized.add(path.get(farthestReachable));
            i = farthestReachable;
        }
        
        return optimized;
    }
    
    private int findFarthestReachablePoint(List<BlockPos> path, int startIndex) {
        int farthest = startIndex + 1;
        
        for (int i = startIndex + 2; i < path.size(); i++) {
            if (hasDirectPath(path.get(startIndex), path.get(i))) {
                farthest = i;
            } else {
                break;
            }
        }
        
        return farthest;
    }
    
    private boolean shouldAddWaypoint(BlockPos start, BlockPos mid, BlockPos end) {
        // Verificar se há uma curva significativa
        Vec3 v1 = new Vec3(mid.getX() - start.getX(), 0, mid.getZ() - start.getZ()).normalize();
        Vec3 v2 = new Vec3(end.getX() - mid.getX(), 0, end.getZ() - mid.getZ()).normalize();
        
        double dotProduct = v1.dotProduct(v2);
        return dotProduct < 0.7; // Ângulo > ~45 graus
    }
    
    // === VERIFICAÇÕES DE TERRENO MELHORADAS ===
    private boolean isWalkableAdvanced(BlockPos pos) {
        if (pos.getY() < 0 || pos.getY() > 255) return false;
        if (dangerousBlocks.contains(pos)) return false;
        
        try {
            if (!world.isBlockLoaded(pos)) return false;
            
            BlockPos ground = pos.down();
            Block groundBlock = world.getBlockState(ground).getBlock();
            Material groundMaterial = groundBlock.getMaterial();
            
            // Verificar se tem chão sólido
            if (!groundMaterial.isSolid() && groundMaterial != Material.water) {
                return false;
            }
            
            // Verificar espaço livre (2 blocos de altura)
            if (!world.isAirBlock(pos) || !world.isAirBlock(pos.up())) {
                return false;
            }
            
            // Verificar materiais perigosos
            if (groundMaterial == Material.lava || groundMaterial == Material.cactus) {
                dangerousBlocks.add(pos);
                return false;
            }
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    private double getTerrainModifier(BlockPos pos) {
        try {
            Block ground = world.getBlockState(pos.down()).getBlock();
            Material material = ground.getMaterial();
            
            if (material == Material.water) return 2.0;
            if (material == Material.ice) return 1.5;
            if (material == Material.sand) return 1.2;
            if (material == Material.grass || material == Material.ground) return 1.0;
            if (material == Material.rock) return 0.9;
            
            return 1.0;
        } catch (Exception e) {
            return 1.0;
        }
    }
    
    // === VERIFICAÇÕES DE MOVIMENTO ESPECÍFICAS ===
    private boolean hasJumpClearance(BlockPos from, BlockPos to) {
        int height = Math.max(2, to.getY() - from.getY() + 2);
        
        for (int i = 1; i <= height; i++) {
            if (!world.isAirBlock(from.up(i))) {
                return false;
            }
        }
        
        return true;
    }
    
    private boolean canFallSafely(BlockPos from, BlockPos to) {
        int fallDistance = from.getY() - to.getY();
        if (fallDistance <= 0 || fallDistance > MAX_SAFE_FALL) return false;
        
        // Verificar se não há obstáculos no caminho da queda
        for (int y = from.getY() - 1; y > to.getY(); y--) {
            BlockPos checkPos = new BlockPos(to.getX(), y, to.getZ());
            if (!world.isAirBlock(checkPos)) return false;
        }
        
        return true;
    }
    
    private boolean isSwimmable(BlockPos pos) {
        return world.getBlockState(pos).getBlock().getMaterial() == Material.water;
    }
    
    // === SISTEMA DE CACHE INTELIGENTE ===
    private List<BlockPos> cachePath(BlockPos start, BlockPos target, List<BlockPos> path) {
        String key = start + "->" + target;
        pathCache.put(key, new ArrayList<>(path));
        
        // Limitar tamanho do cache
        if (pathCache.size() > 100) {
            Iterator<String> it = pathCache.keySet().iterator();
            for (int i = 0; i < 20 && it.hasNext(); i++) {
                it.next();
                it.remove();
            }
        }
        
        return path;
    }
    
    private boolean isPathStillValid(List<BlockPos> path) {
        // Verificar apenas pontos críticos para performance
        int checkPoints = Math.min(5, path.size());
        int step = path.size() / checkPoints;
        
        for (int i = 0; i < checkPoints; i++) {
            int index = Math.min(i * step, path.size() - 1);
            if (!isWalkableAdvanced(path.get(index))) {
                return false;
            }
        }
        
        return true;
    }
    
    // === UTILITÁRIOS ===
    private void preloadChunks(BlockPos start, BlockPos target) {
        // Pré-carregar chunks no caminho para evitar lag
        double dx = target.getX() - start.getX();
        double dz = target.getZ() - start.getZ();
        double distance = Math.sqrt(dx * dx + dz * dz);
        
        int steps = Math.min(10, (int)(distance / 16));
        for (int i = 0; i <= steps; i++) {
            double progress = steps > 0 ? (double) i / steps : 0;
            int x = (int)(start.getX() + dx * progress);
            int z = (int)(start.getZ() + dz * progress);
            
            BlockPos chunkPos = new BlockPos(x, start.getY(), z);
            if (!world.isBlockLoaded(chunkPos)) {
                // Chunk não carregado - pathfinding pode falhar
                System.out.println("[JasperGoTo] Chunk não carregado em: " + chunkPos);
            }
        }
    }
    
    private boolean isOpenArea(BlockPos start, BlockPos target) {
        double distance = getDistance(start, target);
        return distance > 50 && Math.abs(start.getY() - target.getY()) < 10;
    }
    
    private boolean hasDirectPath(BlockPos from, BlockPos to) {
        double distance = getDistance(from, to);
        int steps = Math.max(10, (int)(distance * 2));
        
        for (int i = 1; i < steps; i++) {
            double progress = (double) i / steps;
            BlockPos checkPos = interpolatePosition(from, to, progress);
            
            if (!isWalkableAdvanced(checkPos)) {
                return false;
            }
        }
        
        return true;
    }
    
    private BlockPos interpolatePosition(BlockPos from, BlockPos to, double progress) {
        int x = (int)(from.getX() + (to.getX() - from.getX()) * progress);
        int y = (int)(from.getY() + (to.getY() - from.getY()) * progress);
        int z = (int)(from.getZ() + (to.getZ() - from.getZ()) * progress);
        return new BlockPos(x, y, z);
    }
    
    private BlockPos findNearestSafeGround(BlockPos pos) {
        // Procurar chão seguro em espiral
        for (int radius = 0; radius <= 3; radius++) {
            for (int y = pos.getY() + 3; y >= pos.getY() - 10; y--) {
                for (int x = -radius; x <= radius; x++) {
                    for (int z = -radius; z <= radius; z++) {
                        if (Math.abs(x) != radius && Math.abs(z) != radius) continue;
                        
                        BlockPos testPos = pos.add(x, y - pos.getY(), z);
                        if (isWalkableAdvanced(testPos)) {
                            return testPos;
                        }
                    }
                }
            }
        }
        return pos; // Fallback
    }
    
    private boolean hasObstacle(BlockPos from, BlockPos to) {
        // Verificação rápida de obstáculos diretos
        double dx = to.getX() - from.getX();
        double dz = to.getZ() - from.getZ();
        double distance = Math.sqrt(dx * dx + dz * dz);
        
        if (distance <= 1.5) return false; // Movimentos adjacentes
        
        int steps = (int)(distance * 2);
        for (int i = 1; i < steps; i++) {
            double progress = (double) i / steps;
            int x = (int)(from.getX() + dx * progress);
            int z = (int)(from.getZ() + dz * progress);
            BlockPos checkPos = new BlockPos(x, from.getY(), z);
            
            if (!world.isAirBlock(checkPos) || !world.isAirBlock(checkPos.up())) {
                return true;
            }
        }
        
        return false;
    }
    
    private boolean canJumpToAdvanced(BlockPos from, BlockPos to) {
        int heightDiff = to.getY() - from.getY();
        if (heightDiff > MAX_JUMP_HEIGHT || heightDiff < 0) return false;
        
        double horizontalDistance = Math.sqrt(
            Math.pow(to.getX() - from.getX(), 2) + 
            Math.pow(to.getZ() - from.getZ(), 2)
        );
        
        // Verificar distância máxima de pulo baseada na altura
        double maxJumpDistance = heightDiff == 0 ? 3.5 : (heightDiff == 1 ? 4.0 : SPRINT_JUMP_DISTANCE);
        
        return horizontalDistance <= maxJumpDistance;
    }
    
    private boolean isAtTarget(BlockPos pos, BlockPos target) {
        return pos.equals(target) || getDistance(pos, target) < 1.5;
    }
    
    private double getDistance(BlockPos from, BlockPos to) {
        double dx = to.getX() - from.getX();
        double dy = to.getY() - from.getY();
        double dz = to.getZ() - from.getZ();
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    private List<BlockPos> reconstructPath(Node node) {
        List<BlockPos> path = new ArrayList<>();
        Node current = node;
        
        while (current != null) {
            path.add(0, current.pos);
            current = current.parent;
        }
        
        return path;
    }
    
    private List<BlockPos> createEmergencyPath(BlockPos start, BlockPos target) {
        List<BlockPos> emergencyPath = new ArrayList<>();
        emergencyPath.add(start);
        
        // Criar caminho em linha reta com ajustes de altura
        double distance = getDistance(start, target);
        int waypoints = Math.max(2, (int)(distance / 15));
        
        for (int i = 1; i < waypoints; i++) {
            double progress = (double) i / waypoints;
            BlockPos waypoint = interpolatePosition(start, target, progress);
            waypoint = findNearestSafeGround(waypoint);
            if (waypoint != null) {
                emergencyPath.add(waypoint);
            }
        }
        
        emergencyPath.add(target);
        return emergencyPath;
    }
    
    // === SISTEMA DE MANUTENÇÃO ===
    private void startMaintenanceTask() {
        Timer maintenanceTimer = new Timer("PathfindingMaintenance", true);
        maintenanceTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                performMaintenance();
            }
        }, CACHE_CLEAN_INTERVAL, CACHE_CLEAN_INTERVAL);
    }
    
    private void performMaintenance() {
        long currentTime = System.currentTimeMillis();
        
        // Limpar cache periodicamente
        if (currentTime - lastCacheClean > CACHE_CLEAN_INTERVAL) {
            int originalSize = walkableCache.size();
            walkableCache.clear();
            dangerousBlocks.clear();
            
            // Manter apenas caminhos recentes
            if (pathCache.size() > 50) {
                pathCache.clear();
            }
            
            lastCacheClean = currentTime;
            System.out.println("[JasperGoTo] Cache limpo - " + originalSize + " entradas removidas");
        }
        
        // Log de estatísticas de performance
        if (pathsCalculated > 0) {
            double avgTime = (double) totalCalculationTime / pathsCalculated;
            double cacheHitRate = (double) cacheHits / (cacheHits + cacheMisses) * 100;
            
            System.out.printf("[JasperGoTo] Stats - Caminhos: %d, Tempo médio: %.2fms, Cache: %.1f%%\n",
                pathsCalculated, avgTime, cacheHitRate);
        }
    }
    
    // === MÉTODOS PÚBLICOS DE CONTROLE ===
    
    public List<BlockPos> getCurrentPath() {
        return new ArrayList<>(currentPath);
    }
    
    public void setCurrentPath(List<BlockPos> path) {
        synchronized(currentPath) {
            currentPath.clear();
            if (path != null) {
                currentPath.addAll(path);
            }
        }
    }
    
    public void clearPath() {
        synchronized(currentPath) {
            currentPath.clear();
        }
        targetPos = null;
        
        // Cancelar cálculo em andamento
        if (currentCalculation != null && !currentCalculation.isDone()) {
            currentCalculation.cancel(true);
            shouldStop = true;
        }
    }
    
    public boolean hasPath() {
        return !currentPath.isEmpty();
    }
    
    public BlockPos getTarget() {
        return targetPos;
    }
    
    public void setTarget(BlockPos target) {
        this.targetPos = target;
    }
    
    public boolean isCalculating() {
        return isCalculating;
    }
    
    public void stopCalculation() {
        shouldStop = true;
        if (currentCalculation != null) {
            currentCalculation.cancel(true);
        }
    }
    
    // === MÉTODOS DE NAVEGAÇÃO INTELIGENTE ===
    
    /**
     * Encontra o próximo waypoint no caminho atual
     */
    public BlockPos getNextWaypoint(BlockPos currentPos) {
        if (currentPath.isEmpty()) return null;
        
        // Encontrar o ponto mais próximo no caminho
        int closestIndex = 0;
        double closestDistance = Double.MAX_VALUE;
        
        for (int i = 0; i < currentPath.size(); i++) {
            double distance = getDistance(currentPos, currentPath.get(i));
            if (distance < closestDistance) {
                closestDistance = distance;
                closestIndex = i;
            }
        }
        
        // Se estamos próximos de um waypoint, ir para o próximo
        if (closestDistance < 2.0 && closestIndex < currentPath.size() - 1) {
            return currentPath.get(closestIndex + 1);
        }
        
        return currentPath.get(closestIndex);
    }
    
    /**
     * Verifica se o caminho atual ainda é válido
     */
    public boolean isCurrentPathValid() {
        return isPathStillValid(currentPath);
    }
    
    /**
     * Otimiza o caminho atual removendo waypoints desnecessários
     */
    public void optimizeCurrentPath() {
        if (!currentPath.isEmpty()) {
            List<BlockPos> optimized = optimizePath(new ArrayList<>(currentPath));
            setCurrentPath(optimized);
        }
    }
    
    /**
     * Verifica se precisamos recalcular o caminho
     */
    public boolean needsRecalculation(BlockPos currentPos) {
        if (currentPath.isEmpty() || targetPos == null) return false;
        
        // Se o jogador se afastou muito do caminho
        BlockPos nearestPathPoint = getNearestPathPoint(currentPos);
        if (nearestPathPoint != null && getDistance(currentPos, nearestPathPoint) > 5.0) {
            return true;
        }
        
        // Se o caminho foi bloqueado
        if (!isCurrentPathValid()) {
            return true;
        }
        
        // Se muito tempo passou desde o último cálculo
        long timeSinceLastCalc = System.currentTimeMillis() - lastRecalcTime;
        return timeSinceLastCalc > 30000; // 30 segundos
    }
    
    private BlockPos getNearestPathPoint(BlockPos pos) {
        if (currentPath.isEmpty()) return null;
        
        BlockPos nearest = currentPath.get(0);
        double nearestDistance = getDistance(pos, nearest);
        
        for (BlockPos pathPoint : currentPath) {
            double distance = getDistance(pos, pathPoint);
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearest = pathPoint;
            }
        }
        
        return nearest;
    }
    
    // === MÉTODOS DE ANÁLISE DE TERRENO ===
    
    /**
     * Analisa o terreno ao redor de uma posição
     */
    public TerrainAnalysis analyzeTerrainAround(BlockPos center, int radius) {
        int walkableBlocks = 0;
        int dangerousBlocks = 0;
        int totalBlocks = 0;
        int waterBlocks = 0;
        int lavaBlocks = 0;
        
        for (int x = -radius; x <= radius; x++) {
            for (int z = -radius; z <= radius; z++) {
                for (int y = -2; y <= 2; y++) {
                    BlockPos pos = center.add(x, y, z);
                    totalBlocks++;
                    
                    if (isWalkableAdvanced(pos)) {
                        walkableBlocks++;
                    }
                    
                    try {
                        Material material = world.getBlockState(pos).getBlock().getMaterial();
                        if (material == Material.lava) {
                            lavaBlocks++;
                            dangerousBlocks++;
                        } else if (material == Material.water) {
                            waterBlocks++;
                        } else if (material == Material.cactus) {
                            dangerousBlocks++;
                        }
                    } catch (Exception e) {
                        // Ignorar erros de blocos
                    }
                }
            }
        }
        
        return new TerrainAnalysis(walkableBlocks, dangerousBlocks, waterBlocks, lavaBlocks, totalBlocks);
    }
    
    public static class TerrainAnalysis {
        public final int walkableBlocks;
        public final int dangerousBlocks;
        public final int waterBlocks;
        public final int lavaBlocks;
        public final int totalBlocks;
        public final double walkablePercentage;
        public final double dangerPercentage;
        
        public TerrainAnalysis(int walkable, int dangerous, int water, int lava, int total) {
            this.walkableBlocks = walkable;
            this.dangerousBlocks = dangerous;
            this.waterBlocks = water;
            this.lavaBlocks = lava;
            this.totalBlocks = total;
            this.walkablePercentage = total > 0 ? (double) walkable / total * 100 : 0;
            this.dangerPercentage = total > 0 ? (double) dangerous / total * 100 : 0;
        }
        
        public boolean isSafeArea() {
            return dangerPercentage < 10 && walkablePercentage > 30;
        }
        
        public boolean isComplexTerrain() {
            return walkablePercentage < 50;
        }
    }
    
    // === MÉTODOS DE DEBUG E DIAGNÓSTICO ===
    
    /**
     * Obtém informações detalhadas sobre o estado do pathfinding
     */
    public PathfindingStatus getStatus() {
        return new PathfindingStatus(
            isCalculating,
            currentPath.size(),
            targetPos,
            pathsCalculated,
            totalCalculationTime,
            cacheHits,
            cacheMisses,
            walkableCache.size(),
            pathCache.size()
        );
    }
    
    public static class PathfindingStatus {
        public final boolean isCalculating;
        public final int currentPathLength;
        public final BlockPos target;
        public final int totalPathsCalculated;
        public final long totalCalculationTime;
        public final int cacheHits;
        public final int cacheMisses;
        public final int walkableCacheSize;
        public final int pathCacheSize;
        public final double averageCalculationTime;
        public final double cacheHitRate;
        
        public PathfindingStatus(boolean calculating, int pathLength, BlockPos target,
                               int totalPaths, long totalTime, int hits, int misses,
                               int walkableCache, int pathCache) {
            this.isCalculating = calculating;
            this.currentPathLength = pathLength;
            this.target = target;
            this.totalPathsCalculated = totalPaths;
            this.totalCalculationTime = totalTime;
            this.cacheHits = hits;
            this.cacheMisses = misses;
            this.walkableCacheSize = walkableCache;
            this.pathCacheSize = pathCache;
            
            this.averageCalculationTime = totalPaths > 0 ? (double) totalTime / totalPaths : 0;
            this.cacheHitRate = (hits + misses) > 0 ? (double) hits / (hits + misses) * 100 : 0;
        }
        
        @Override
        public String toString() {
            return String.format(
                "PathfindingStatus{calculating=%s, pathLength=%d, target=%s, " +
                "avgTime=%.2fms, cacheRate=%.1f%%, caches=[walkable:%d, path:%d]}",
                isCalculating, currentPathLength, target,
                averageCalculationTime, cacheHitRate, walkableCacheSize, pathCacheSize
            );
        }
    }
    
    /**
     * Força limpeza de todos os caches
     */
    public void clearAllCaches() {
        walkableCache.clear();
        pathCache.clear();
        dangerousBlocks.clear();
        System.out.println("[JasperGoTo] Todos os caches foram limpos");
    }
    
    /**
     * Reinicia todas as estatísticas
     */
    public void resetStatistics() {
        totalCalculationTime = 0;
        pathsCalculated = 0;
        cacheHits = 0;
        cacheMisses = 0;
        System.out.println("[JasperGoTo] Estatísticas resetadas");
    }
    
    // === MÉTODOS UTILITÁRIOS AVANÇADOS ===
    
    /**
     * Calcula múltiplos caminhos alternativos
     */
    public CompletableFuture<List<List<BlockPos>>> findAlternativePaths(BlockPos start, BlockPos target, int numPaths) {
        return CompletableFuture.supplyAsync(() -> {
            List<List<BlockPos>> alternatives = new ArrayList<>();
            Set<BlockPos> usedNodes = new HashSet<>();
            
            for (int i = 0; i < numPaths && i < 3; i++) {
                List<BlockPos> path = calculateAlternativePath(start, target, usedNodes);
                if (path != null && !path.isEmpty()) {
                    alternatives.add(path);
                    // Adicionar nós usados para evitar caminhos idênticos
                    path.stream().limit(path.size() / 2).forEach(usedNodes::add);
                }
            }
            
            return alternatives;
        }, pathfindingExecutor);
    }
    
    private List<BlockPos> calculateAlternativePath(BlockPos start, BlockPos target, Set<BlockPos> avoid) {
        // Implementação simplificada - penalizar nós a evitar
        return performAStarWithAvoidance(start, target, avoid);
    }
    
    private List<BlockPos> performAStarWithAvoidance(BlockPos start, BlockPos target, Set<BlockPos> avoid) {
        // Similar ao A* normal, mas com penalização extra para nós a evitar
        PriorityQueue<Node> openSet = new PriorityQueue<>();
        Set<BlockPos> closedSet = new HashSet<>();
        Map<BlockPos, Double> bestG = new HashMap<>();
        
        Node startNode = new Node(start, null, 0, getAdvancedHeuristic(start, target), MovementType.WALK);
        openSet.add(startNode);
        bestG.put(start, 0.0);
        
        int iterations = 0;
        
        while (!openSet.isEmpty() && iterations < MAX_ITERATIONS / 2) {
            iterations++;
            
            Node current = openSet.poll();
            
            if (isAtTarget(current.pos, target)) {
                return reconstructPath(current);
            }
            
            closedSet.add(current.pos);
            
            for (NeighborInfo neighbor : getAdvancedNeighbors(current.pos)) {
                if (closedSet.contains(neighbor.pos)) continue;
                
                double tentativeG = current.g + neighbor.cost;
                
                // Penalização extra para nós a evitar
                if (avoid.contains(neighbor.pos)) {
                    tentativeG += 10.0;
                }
                
                if (bestG.containsKey(neighbor.pos) && tentativeG >= bestG.get(neighbor.pos)) {
                    continue;
                }
                
                bestG.put(neighbor.pos, tentativeG);
                
                Node neighborNode = new Node(
                    neighbor.pos, current, tentativeG,
                    getAdvancedHeuristic(neighbor.pos, target),
                    neighbor.movementType
                );
                
                openSet.add(neighborNode);
            }
        }
        
        return null; // Nenhum caminho alternativo encontrado
    }
    
    // === CLEANUP ===
    
    /**
     * Limpa recursos e para threads
     */
    public void shutdown() {
        shouldStop = true;
        
        if (currentCalculation != null) {
            currentCalculation.cancel(true);
        }
        
        pathfindingExecutor.shutdown();
        try {
            if (!pathfindingExecutor.awaitTermination(2, TimeUnit.SECONDS)) {
                pathfindingExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            pathfindingExecutor.shutdownNow();
        }
        
        clearAllCaches();
        System.out.println("[JasperGoTo] Sistema de pathfinding desligado");
    }
}