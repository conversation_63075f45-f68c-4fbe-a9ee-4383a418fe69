package com.jaspergoto.pathfinding;

import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.WorldRenderer;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.BlockPos;
import net.minecraftforge.client.event.RenderWorldLastEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import org.lwjgl.opengl.GL11;

import java.util.List;

/**
 * Renderizador melhorado de caminhos.
 * 
 * Características:
 * - Blocos com outline limpo
 * - Preenchimento semi-transparente
 * - Linhas conectando os waypoints
 * - Mais waypoints visíveis
 * - Visual clean e profissional
 */
public class PathRenderer {
    
    private final PathfindingEngine pathfinding;
    
    public PathRenderer(PathfindingEngine pathfinding) {
        this.pathfinding = pathfinding;
    }
    
    @SubscribeEvent
    public void onRenderWorldLast(RenderWorldLastEvent event) {
        if (!pathfinding.hasPath()) return;
        
        EntityPlayer player = Minecraft.getMinecraft().thePlayer;
        
        // Interpolação suave da posição do jogador
        double playerX = player.lastTickPosX + (player.posX - player.lastTickPosX) * event.partialTicks;
        double playerY = player.lastTickPosY + (player.posY - player.lastTickPosY) * event.partialTicks;
        double playerZ = player.lastTickPosZ + (player.posZ - player.lastTickPosZ) * event.partialTicks;
        
        GlStateManager.pushMatrix();
        GlStateManager.translate(-playerX, -playerY, -playerZ);
        
        // Estados de renderização
        GlStateManager.disableTexture2D();
        GlStateManager.disableLighting();
        GlStateManager.enableBlend();
        GlStateManager.blendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);
        GlStateManager.disableDepth();
        
        List<BlockPos> path = pathfinding.getCurrentPath();
        
        // Renderizar linha conectando os waypoints
        renderPathLine(path);
        
        // Renderizar waypoints minimalistas (mais blocos)
        renderEnhancedWaypoints(path);
        
        // Renderizar destino final
        if (pathfinding.getTarget() != null) {
            renderTargetBlock(pathfinding.getTarget());
        }
        
        // Restaurar estados
        GlStateManager.enableDepth();
        GlStateManager.disableBlend();
        GlStateManager.enableLighting();
        GlStateManager.enableTexture2D();
        GlStateManager.popMatrix();
    }
    
    private void renderPathLine(List<BlockPos> path) {
        if (path.size() < 2) return;
        
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer worldRenderer = tessellator.getWorldRenderer();
        
        GL11.glLineWidth(3.0f);
        worldRenderer.begin(GL11.GL_LINE_STRIP, DefaultVertexFormats.POSITION_COLOR);
        
        for (int i = 0; i < path.size(); i++) {
            BlockPos pos = path.get(i);
            float progress = (float) i / Math.max(1, path.size() - 1);
            
            // Cor gradiente do azul para verde
            float r = progress * 0.3f;
            float g = 0.7f + progress * 0.3f;
            float b = 1.0f - progress * 0.5f;
            float a = 0.8f;
            
            worldRenderer.pos(pos.getX() + 0.5, pos.getY() + 0.5, pos.getZ() + 0.5)
                        .color(r, g, b, a).endVertex();
        }
        
        tessellator.draw();
    }
    
    private void renderEnhancedWaypoints(List<BlockPos> path) {
        for (int i = 0; i < path.size(); i++) {
            BlockPos pos = path.get(i);
            
            // Mostrar mais waypoints (a cada 2 blocos em vez de 3)
            if (i % 2 != 0 && i != 0 && i != path.size() - 1) continue;
            
            // Cor baseada no progresso
            float progress = (float) i / Math.max(1, path.size() - 1);
            float r = 0.2f + progress * 0.5f;
            float g = 0.6f + progress * 0.4f;
            float b = 1.0f - progress * 0.4f;
            
            renderCleanBlock(pos, r, g, b, 0.3f, 0.7f);
        }
    }
    
    private void renderTargetBlock(BlockPos target) {
        // Destino em verde brilhante
        renderCleanBlock(target, 0.0f, 1.0f, 0.2f, 0.4f, 1.0f);
    }
    
    private void renderCleanBlock(BlockPos pos, float r, float g, float b, float fillAlpha, float outlineAlpha) {
        double x = pos.getX();
        double y = pos.getY();
        double z = pos.getZ();
        
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer worldRenderer = tessellator.getWorldRenderer();
        
        // 1. Renderizar preenchimento semi-transparente
        worldRenderer.begin(GL11.GL_QUADS, DefaultVertexFormats.POSITION_COLOR);
        
        // Face inferior
        worldRenderer.pos(x, y, z).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x + 1, y, z).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x + 1, y, z + 1).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x, y, z + 1).color(r, g, b, fillAlpha).endVertex();
        
        // Face superior
        worldRenderer.pos(x, y + 1, z).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x, y + 1, z + 1).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x + 1, y + 1, z + 1).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x + 1, y + 1, z).color(r, g, b, fillAlpha).endVertex();
        
        // Face norte
        worldRenderer.pos(x, y, z).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x, y + 1, z).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x + 1, y + 1, z).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x + 1, y, z).color(r, g, b, fillAlpha).endVertex();
        
        // Face sul
        worldRenderer.pos(x, y, z + 1).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x + 1, y, z + 1).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x + 1, y + 1, z + 1).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x, y + 1, z + 1).color(r, g, b, fillAlpha).endVertex();
        
        // Face oeste
        worldRenderer.pos(x, y, z).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x, y, z + 1).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x, y + 1, z + 1).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x, y + 1, z).color(r, g, b, fillAlpha).endVertex();
        
        // Face leste
        worldRenderer.pos(x + 1, y, z).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x + 1, y + 1, z).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x + 1, y + 1, z + 1).color(r, g, b, fillAlpha).endVertex();
        worldRenderer.pos(x + 1, y, z + 1).color(r, g, b, fillAlpha).endVertex();
        
        tessellator.draw();
        
        // 2. Renderizar outline limpo
        GL11.glLineWidth(2.0f);
        worldRenderer.begin(GL11.GL_LINES, DefaultVertexFormats.POSITION_COLOR);
        
        // Arestas horizontais inferiores
        worldRenderer.pos(x, y, z).color(r, g, b, outlineAlpha).endVertex();
        worldRenderer.pos(x + 1, y, z).color(r, g, b, outlineAlpha).endVertex();
        
        worldRenderer.pos(x + 1, y, z).color(r, g, b, outlineAlpha).endVertex();
        worldRenderer.pos(x + 1, y, z + 1).color(r, g, b, outlineAlpha).endVertex();
        
        worldRenderer.pos(x + 1, y, z + 1).color(r, g, b, outlineAlpha).endVertex();
        worldRenderer.pos(x, y, z + 1).color(r, g, b, outlineAlpha).endVertex();
        
        worldRenderer.pos(x, y, z + 1).color(r, g, b, outlineAlpha).endVertex();
        worldRenderer.pos(x, y, z).color(r, g, b, outlineAlpha).endVertex();
        
        // Arestas horizontais superiores
        worldRenderer.pos(x, y + 1, z).color(r, g, b, outlineAlpha).endVertex();
        worldRenderer.pos(x + 1, y + 1, z).color(r, g, b, outlineAlpha).endVertex();
        
        worldRenderer.pos(x + 1, y + 1, z).color(r, g, b, outlineAlpha).endVertex();
        worldRenderer.pos(x + 1, y + 1, z + 1).color(r, g, b, outlineAlpha).endVertex();
        
        worldRenderer.pos(x + 1, y + 1, z + 1).color(r, g, b, outlineAlpha).endVertex();
        worldRenderer.pos(x, y + 1, z + 1).color(r, g, b, outlineAlpha).endVertex();
        
        worldRenderer.pos(x, y + 1, z + 1).color(r, g, b, outlineAlpha).endVertex();
        worldRenderer.pos(x, y + 1, z).color(r, g, b, outlineAlpha).endVertex();
        
        // Arestas verticais
        worldRenderer.pos(x, y, z).color(r, g, b, outlineAlpha).endVertex();
        worldRenderer.pos(x, y + 1, z).color(r, g, b, outlineAlpha).endVertex();
        
        worldRenderer.pos(x + 1, y, z).color(r, g, b, outlineAlpha).endVertex();
        worldRenderer.pos(x + 1, y + 1, z).color(r, g, b, outlineAlpha).endVertex();
        
        worldRenderer.pos(x + 1, y, z + 1).color(r, g, b, outlineAlpha).endVertex();
        worldRenderer.pos(x + 1, y + 1, z + 1).color(r, g, b, outlineAlpha).endVertex();
        
        worldRenderer.pos(x, y, z + 1).color(r, g, b, outlineAlpha).endVertex();
        worldRenderer.pos(x, y + 1, z + 1).color(r, g, b, outlineAlpha).endVertex();
        
        tessellator.draw();
    }
}