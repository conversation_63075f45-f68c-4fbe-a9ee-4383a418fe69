package com.jaspergoto.pathfinding;

import net.minecraft.block.Block;
import net.minecraft.client.Minecraft;
import net.minecraft.client.entity.EntityPlayerSP;
import net.minecraft.client.settings.KeyBinding;
import net.minecraft.util.BlockPos;
import net.minecraft.util.ChatComponentText;
import net.minecraft.util.EnumChatFormatting;
import net.minecraft.util.MathHelper;
import net.minecraft.util.Vec3;
import net.minecraft.util.MovingObjectPosition;
import net.minecraft.world.World;
import net.minecraft.block.material.Material;

import java.util.List;

/**
 * Controlador de movimento com câmera rápida e fluida.
 *
 * Melhorias implementadas:
 * - Câmera rápida como movimento de mouse natural
 * - Interpolação suave sem microtravadas
 * - Sistema inteligente de pulo baseado no terreno
 * - Movimento otimizado
 */
public class MovementController {

    // Configurações otimizadas
    private static final long JUMP_COOLDOWN_MS = 400L;
    private static final double WAYPOINT_REACH_DISTANCE = 0.8; // Smaller reach distance for tighter waypoint following
    private static final double DYNAMIC_SKIP_DISTANCE = 0.4; // Much smaller for very dense waypoint following

    // Câmera PROFISSIONAL: deadzone, previsão e Bézier
    private static final float CAMERA_SPEED_MULTIPLIER = 1.8f; // menos agressiva
    private static final float CAMERA_SMOOTHING = 0.25f; // mais suavização
    private static final float MAX_ROTATION_SPEED = 35.0f; // limita p/ evitar travadas
    private static final float CAMERA_DEADZONE_DEG = 2.5f; // ignora micro variações
    private static final int CAMERA_LOOKAHEAD_WPS = 4; // olhar alguns WPs à frente

    // Interpolação para suavizar a câmera entre alvo imediato e lookahead
    private static final float INTERPOLATION_FACTOR = 0.4f; // mistura com lookahead para suavizar

    // Evitar colisões e analisar terreno
    private static final double OBSTACLE_DETECT_DIST = 3.5; // distância maior para detectar parede
    private static final float SIDE_SAMPLE_ANGLE = 30.0f;   // ângulo base para tentar desviar
    private static final float SIDE_SAMPLE_STEP = 8.0f;    // passo entre amostras de desvio
    private static final int SIDE_SAMPLE_COUNT = 5;         // mais amostras por lado
    private static final double WALL_AVOID_DISTANCE = 1.5;
    private static final float SIDE_TURN_ANGLE = 90.0f; // ângulo para virar ao contornar parede
    private static final int SIDE_AVOID_TICKS = 6; // mantêm desvio lateral por mais tempo

    // Sistema anti-travamento
    private static final int BACKWARD_TICKS_ON_STUCK = 4;
    private static final int STUCK_THRESHOLD = 20;

    private final Minecraft mc = Minecraft.getMinecraft();
    private final PathfindingEngine pathfinding;

    // Estado do movimento
    private int currentWaypointIndex = 0;
    private BlockPos currentTarget = null;

    // Detecção de travamento
    private BlockPos lastPosition = null;
    private int stuckCounter = 0;

    // Controle de pulo avançado
    private long lastJumpTime = 0;
    private BlockPos lastJumpPos = null;
    private int ticksSinceLastJump = 0;
    private boolean wasOnGround = true;
    private boolean needsJumpNext = false;

    // Controle temporário de teclas
    private int backwardTicks = 0;
    private int sideAvoidTicks = 0;

    public MovementController(PathfindingEngine pathfinding) {
        this.pathfinding = pathfinding;
    }

    public void update() {
        if (!pathfinding.hasPath()) {
            return;
        }

        EntityPlayerSP player = mc.thePlayer;
        if (player == null) return;

        List<BlockPos> path = pathfinding.getCurrentPath();

        // Chegou ao destino?
        if (currentWaypointIndex >= path.size()) {
            player.addChatMessage(new ChatComponentText(
                EnumChatFormatting.YELLOW + "[JasperGoTo] " +
                EnumChatFormatting.GREEN + "✓ Destino alcançado!"
            ));
            pathfinding.clearPath();
            stopMovement();
            return;
        }

        // Escolher melhor waypoint
        currentTarget = findBestWaypoint(player, path);

        // Pré-calcular necessidade de pulo
        analyzeUpcomingTerrain(player, path);

        // Verificar se está preso
        checkIfStuck(player);

        // Aplicar movimento
        applyMovement(player, currentTarget);

        // Atualizar progresso
        updateWaypointProgress(player, path);
    }

    private void applyMovement(EntityPlayerSP player, BlockPos target) {
        // Calcular direção
        double dx = target.getX() + 0.5 - player.posX;
        double dy = target.getY() - player.posY;
        double dz = target.getZ() + 0.5 - player.posZ;
        double horizontalDist = Math.sqrt(dx * dx + dz * dz);

        // Calcular rotações alvo
        float targetYaw = (float) Math.toDegrees(Math.atan2(-dx, dz));
        float targetPitch = 0.0f;
        if (horizontalDist > 0.01) {
            targetPitch = (float) Math.toDegrees(Math.atan2(-dy, horizontalDist));
        }

        // Ajustar objetivo de olhar para evitar paredes (steering por raycast)
        float[] steered = steerAroundObstacles(player, targetYaw, targetPitch);

        // Aplicar câmera profissional com deadzone e previsão (olhar adiante vários WPs)
        float[] predicted = predictYawPitchAhead(player, steered[0], steered[1]);
        applyProfessionalCamera(player, predicted[0], predicted[1]);

        // Verificar parede à frente e iniciar desvio lateral se necessário
        if (isObstructedForward(player, player.rotationYaw, player.rotationPitch, WALL_AVOID_DISTANCE)) {
            double leftFree = freeDistanceForward(player, player.rotationYaw + SIDE_TURN_ANGLE, player.rotationPitch, WALL_AVOID_DISTANCE);
            double rightFree = freeDistanceForward(player, player.rotationYaw - SIDE_TURN_ANGLE, player.rotationPitch, WALL_AVOID_DISTANCE);
            if (leftFree > rightFree) {
                KeyBinding.setKeyBindState(mc.gameSettings.keyBindLeft.getKeyCode(), true);
                sideAvoidTicks = SIDE_AVOID_TICKS;
            } else {
                KeyBinding.setKeyBindState(mc.gameSettings.keyBindRight.getKeyCode(), true);
                sideAvoidTicks = SIDE_AVOID_TICKS;
            }
            // Não avançar diretamente para a parede enquanto desvia
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindForward.getKeyCode(), false);
        } else if (sideAvoidTicks <= 0) {
            // Movimento para frente quando não há parede nem desvio ativo
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindForward.getKeyCode(), true);
        }

        // Sprint inteligente
        float yawDiff = MathHelper.wrapAngleTo180_float(targetYaw - player.rotationYaw);
        boolean shouldSprint = horizontalDist > 2.5 && Math.abs(yawDiff) < 25.0f;
        player.setSprinting(shouldSprint);

        // Sistema de pulo inteligente
        handleAdvancedJumping(player, target, horizontalDist);

        // Desabilitar agachamento e limpar desvios laterais (serão resetados em onTick)
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindSneak.getKeyCode(), false);
    }

    private void applyFastSmoothCamera(EntityPlayerSP player, float targetYaw, float targetPitch) {
        // Calcular diferenças angulares
        float yawDiff = MathHelper.wrapAngleTo180_float(targetYaw - player.rotationYaw);
        float pitchDiff = targetPitch - player.rotationPitch;

        // Aplicar velocidade base alta
        float yawSpeed = yawDiff * CAMERA_SPEED_MULTIPLIER;
        float pitchSpeed = pitchDiff * CAMERA_SPEED_MULTIPLIER;

        // Limitar velocidade máxima para evitar saltos bruscos
        yawSpeed = MathHelper.clamp_float(yawSpeed, -MAX_ROTATION_SPEED, MAX_ROTATION_SPEED);
        pitchSpeed = MathHelper.clamp_float(pitchSpeed, -MAX_ROTATION_SPEED, MAX_ROTATION_SPEED);

        // Aplicar suavização mínima apenas para eliminar microtravadas
        float smoothYaw = yawSpeed * (1.0f - CAMERA_SMOOTHING) + yawSpeed * CAMERA_SMOOTHING;
        float smoothPitch = pitchSpeed * (1.0f - CAMERA_SMOOTHING) + pitchSpeed * CAMERA_SMOOTHING;

        // Aplicar rotação diretamente (como movimento de mouse)
        player.rotationYaw += smoothYaw;
        player.rotationPitch += smoothPitch;

        // Limitar pitch
        player.rotationPitch = MathHelper.clamp_float(player.rotationPitch, -90.0f, 90.0f);
    }

    // Tenta desviar a câmera do alvo se houver obstáculo à frente
    // Retorna yaw/pitch ajustados que apontam para uma direção livre
    private float[] steerAroundObstacles(EntityPlayerSP player, float targetYaw, float targetPitch) {
        // Primeiro, checar se há parede muito próxima (WALL_AVOID_DISTANCE)
        if (isObstructedForward(player, targetYaw, targetPitch, WALL_AVOID_DISTANCE)) {
            double leftFree = freeDistanceForward(player, targetYaw + SIDE_TURN_ANGLE, targetPitch, WALL_AVOID_DISTANCE);
            double rightFree = freeDistanceForward(player, targetYaw - SIDE_TURN_ANGLE, targetPitch, WALL_AVOID_DISTANCE);
            if (leftFree > rightFree) {
                return new float[]{targetYaw + SIDE_TURN_ANGLE, targetPitch};
            } else {
                return new float[]{targetYaw - SIDE_TURN_ANGLE, targetPitch};
            }
        }

        // Se frente está livre dentro do alcance de detecção padrão, manter alvo
        if (!isObstructedForward(player, targetYaw, targetPitch, OBSTACLE_DETECT_DIST)) {
            return new float[]{targetYaw, targetPitch};
        }

        // Caso ainda haja obstáculo, usar amostragem lateral para encontrar melhor direção
        float bestYaw = targetYaw;
        double bestFreeDist = 0.0;
        for (int i = 0; i < SIDE_SAMPLE_COUNT; i++) {
            float delta = SIDE_SAMPLE_ANGLE + i * SIDE_SAMPLE_STEP;
            float leftYaw = targetYaw + delta;
            double leftFree = freeDistanceForward(player, leftYaw, targetPitch, OBSTACLE_DETECT_DIST);
            if (leftFree > bestFreeDist) {
                bestFreeDist = leftFree;
                bestYaw = leftYaw;
            }
            float rightYaw = targetYaw - delta;
            double rightFree = freeDistanceForward(player, rightYaw, targetPitch, OBSTACLE_DETECT_DIST);
            if (rightFree > bestFreeDist) {
                bestFreeDist = rightFree;
                bestYaw = rightYaw;
            }
        }
        return new float[]{bestYaw, targetPitch};
    }

    private double freeDistanceForward(EntityPlayerSP player, float yaw, float pitch, double maxDist) {
        World w = mc.theWorld;
        if (w == null) return maxDist;
        Vec3 eye = player.getPositionEyes(1.0f);
        Vec3 dir = directionFromYawPitch(yaw, pitch);
        Vec3 to = eye.addVector(dir.xCoord * maxDist, dir.yCoord * maxDist, dir.zCoord * maxDist);
        MovingObjectPosition mop = w.rayTraceBlocks(eye, to, false, true, false);
        if (mop == null) return maxDist;
        // calcular a fração percorrida
        double hitDist = eye.distanceTo(mop.hitVec);
        return Math.max(0.0, Math.min(maxDist, hitDist));
    }

    private boolean isObstructedForward(EntityPlayerSP player, float yaw, float pitch, double distance) {
        return freeDistanceForward(player, yaw, pitch, distance) < distance * 0.98; // quase atingiu algo
    }

    private Vec3 directionFromYawPitch(float yaw, float pitch) {
        // Converter yaw/pitch em vetor direção (mesma convenção do Minecraft)
        float yawRad = (float) Math.toRadians(-yaw - 180.0f);
        float pitchRad = (float) Math.toRadians(-pitch);
        float cosYaw = MathHelper.cos(yawRad);
        float sinYaw = MathHelper.sin(yawRad);
        float cosPitch = MathHelper.cos(pitchRad);
        float sinPitch = MathHelper.sin(pitchRad);
        return new Vec3((double)(sinYaw * cosPitch), (double)sinPitch, (double)(cosYaw * cosPitch));
    }

    // Predição de câmera olhando adiante alguns waypoints
    private float[] predictYawPitchAhead(EntityPlayerSP player, float fallbackYaw, float fallbackPitch) {
        // Use a multi‑waypoint average for a smoother, less robotic look direction
        float[] smooth = computeSmoothLookahead(player);
        float targetYaw = smooth[0];
        float targetPitch = smooth[1];

        // Blend with the immediate fallback (current view) to keep motion fluid
        float blendedYaw = fallbackYaw + (targetYaw - fallbackYaw) * INTERPOLATION_FACTOR;
        float blendedPitch = fallbackPitch + (targetPitch - fallbackPitch) * INTERPOLATION_FACTOR;

        // If the blended direction is blocked, fall back to the immediate view
        if (isObstructedForward(player, blendedYaw, blendedPitch, OBSTACLE_DETECT_DIST)) {
            return new float[]{fallbackYaw, fallbackPitch};
        }
        return new float[]{blendedYaw, blendedPitch};
    }

    // New helper: compute a smooth target based on several upcoming waypoints
    private float[] computeSmoothLookahead(EntityPlayerSP player) {
        List<BlockPos> path = pathfinding.getCurrentPath();
        if (path.isEmpty() || currentWaypointIndex >= path.size()) {
            return new float[]{player.rotationYaw, player.rotationPitch};
        }
        // Média ponderada de alguns próximos waypoints para direção mais natural
        int end = Math.min(path.size(), currentWaypointIndex + CAMERA_LOOKAHEAD_WPS);
        double sumX = 0, sumY = 0, sumZ = 0, weightSum = 0;
        for (int i = currentWaypointIndex; i < end; i++) {
            BlockPos wp = path.get(i);
            double w = 1.0 / (1 + (i - currentWaypointIndex)); // pesos decrescentes
            sumX += (wp.getX() + 0.5) * w;
            sumY += wp.getY() * w;
            sumZ += (wp.getZ() + 0.5) * w;
            weightSum += w;
        }
        double tx = sumX / weightSum;
        double ty = sumY / weightSum;
        double tz = sumZ / weightSum;

        double dx = tx - player.posX;
        double dy = ty - player.posY;
        double dz = tz - player.posZ;
        double horiz = Math.sqrt(dx * dx + dz * dz);
        float yaw = (float) Math.toDegrees(Math.atan2(-dx, dz));
        float pitch = horiz > 0.01 ? (float) Math.toDegrees(Math.atan2(-dy, horiz)) : player.rotationPitch;
        // Limitar pitch como movimento de cabeça humano
        pitch = MathHelper.clamp_float(pitch, -25.0f, 25.0f);
        return new float[]{yaw, pitch};
    }

    // Câmera com deadzone e easing
    private void applyProfessionalCamera(EntityPlayerSP player, float targetYaw, float targetPitch) {
        float yawDiff = MathHelper.wrapAngleTo180_float(targetYaw - player.rotationYaw);
        float pitchDiff = targetPitch - player.rotationPitch;

        // Deadzone para evitar micro-ajustes
        if (Math.abs(yawDiff) < CAMERA_DEADZONE_DEG) yawDiff = 0;
        if (Math.abs(pitchDiff) < CAMERA_DEADZONE_DEG) pitchDiff = 0;

        // Easing Bézier simplificado via curva ease-in-out (S-curve)
        float ease = 0.12f; // passo base
        float factor = (float)(0.5 - 0.5 * Math.cos(Math.min(1f, (Math.abs(yawDiff)+Math.abs(pitchDiff))/90f) * Math.PI));
        float yawStep = MathHelper.clamp_float(yawDiff * (ease + factor * 0.4f) * CAMERA_SPEED_MULTIPLIER, -MAX_ROTATION_SPEED, MAX_ROTATION_SPEED);
        float pitchStep = MathHelper.clamp_float(pitchDiff * (ease + factor * 0.3f) * CAMERA_SPEED_MULTIPLIER, -MAX_ROTATION_SPEED, MAX_ROTATION_SPEED);

        player.rotationYaw += yawStep;
        player.rotationPitch = MathHelper.clamp_float(player.rotationPitch + pitchStep, -90.0f, 90.0f);
    }

    private void handleAdvancedJumping(EntityPlayerSP player, BlockPos target, double horizontalDist) {
        ticksSinceLastJump++;

        // Só considerar pulo se estiver no chão há alguns ticks
        if (!player.onGround || ticksSinceLastJump < 2) {
            wasOnGround = player.onGround;
            return;
        }

        // Cooldown de pulo
        long now = System.currentTimeMillis();
        if (now - lastJumpTime < JUMP_COOLDOWN_MS) return;

        // Não pular no mesmo local
        BlockPos curPos = new BlockPos(player.posX, player.posY, player.posZ);
        if (lastJumpPos != null && lastJumpPos.equals(curPos)) return;

        // Análise inteligente se precisa pular
        if (needsAdvancedJump(player, target, horizontalDist)) {
            player.jump();
            lastJumpTime = now;
            lastJumpPos = curPos;
            ticksSinceLastJump = 0;
        }

        wasOnGround = player.onGround;
    }

    private boolean needsAdvancedJump(EntityPlayerSP player, BlockPos target, double horizontalDist) {
        World world = mc.theWorld;
        BlockPos playerPos = new BlockPos(player.posX, player.posY, player.posZ);

        // 1. Verificar se o target está acima
        double heightDiff = target.getY() - player.posY;
        if (heightDiff < 0.5) return false;

        // 2. Verificar se há bloco sólido imediatamente à frente
        Vec3 look = player.getLookVec();
        BlockPos frontBlock = playerPos.add(
            MathHelper.floor_double(look.xCoord),
            0,
            MathHelper.floor_double(look.zCoord)
        );

        Block frontBlockType = world.getBlockState(frontBlock).getBlock();
        if (!frontBlockType.getMaterial().isSolid()) {
            if (!needsJumpNext) return false;
        }

        // 3. Verificar espaço acima da cabeça
        if (!world.isAirBlock(playerPos.up()) || !world.isAirBlock(playerPos.up(2))) {
            return false;
        }

        // 4. Verificar se está colidindo horizontalmente
        if (!player.isCollidedHorizontally && heightDiff < 1.0) {
            return false;
        }

        // 5. Verificar se o pulo é necessário
        if (needsJumpNext || player.isCollidedHorizontally) {
            return true;
        }

        // 6. Último recurso: verificar se está preso
        if (ticksSinceLastJump > 30 && player.isCollidedHorizontally) {
            return true;
        }

        return false;
    }

    private void analyzeUpcomingTerrain(EntityPlayerSP player, List<BlockPos> path) {
        needsJumpNext = false;

        if (currentWaypointIndex >= path.size() - 1) return;

        BlockPos playerPos = new BlockPos(player.posX, player.posY, player.posZ);

        // Examine a larger set of upcoming waypoints (up to 7) for height changes and hazards
        int lookahead = Math.min(7, path.size() - currentWaypointIndex);
        World world = mc.theWorld;
        for (int i = 0; i < lookahead; i++) {
            BlockPos wp = path.get(currentWaypointIndex + i);
            double heightDiff = wp.getY() - playerPos.getY();
            if (heightDiff > 0.5) {
                needsJumpNext = true;
                break;
            }
            // Detect dangerous blocks (lava, fire, water) at the waypoint
            if (world != null) {
                Block block = world.getBlockState(wp).getBlock();
                Material mat = block.getMaterial();
                if (mat == Material.lava || mat == Material.water || mat == Material.fire) {
                    // Mark as needing to avoid; could trigger side avoidance later
                    // For now, we simply set a flag to avoid jumping into it
                    needsJumpNext = false;
                }
            }
        }

        // Detect a wall directly ahead within a short distance; if there is headroom, prepare a jump
        if (isObstructedForward(player, player.rotationYaw, player.rotationPitch, WALL_AVOID_DISTANCE)) {
            if (world != null) {
                BlockPos front = new BlockPos(
                    player.posX + MathHelper.cos((float)Math.toRadians(player.rotationYaw)) * 0.6,
                    player.posY,
                    player.posZ + MathHelper.sin((float)Math.toRadians(player.rotationYaw)) * 0.6);
                boolean headroom = world.isAirBlock(front.up()) && world.isAirBlock(front.up(2));
                if (headroom) {
                    needsJumpNext = true;
                }
            }
        }
    }

    private BlockPos findBestWaypoint(EntityPlayerSP player, List<BlockPos> path) {
        for (int i = currentWaypointIndex; i < Math.min(currentWaypointIndex + 4, path.size()); i++) {
            BlockPos wp = path.get(i);
            double dist = getDistanceToBlock(player, wp);

            if (dist < DYNAMIC_SKIP_DISTANCE && i < path.size() - 1) {
                currentWaypointIndex = i + 1;
                continue;
            }
            return wp;
        }
        return path.get(Math.min(currentWaypointIndex, path.size() - 1));
    }

    private void updateWaypointProgress(EntityPlayerSP player, List<BlockPos> path) {
        if (currentWaypointIndex >= path.size()) return;

        BlockPos cur = path.get(currentWaypointIndex);
        double distance = getDistanceToBlock(player, cur);

        if (distance < WAYPOINT_REACH_DISTANCE) {
            currentWaypointIndex++;
            return;
        }

        if (currentWaypointIndex < path.size() - 1) {
            BlockPos next = path.get(currentWaypointIndex + 1);
            double nextDist = getDistanceToBlock(player, next);
            if (nextDist < distance && nextDist < WAYPOINT_REACH_DISTANCE * 1.3) {
                currentWaypointIndex++;
            }
        }
    }

    private void checkIfStuck(EntityPlayerSP player) {
        BlockPos cur = new BlockPos(player.posX, player.posY, player.posZ);

        if (lastPosition != null && lastPosition.equals(cur)) {
            stuckCounter++;
            if (stuckCounter > STUCK_THRESHOLD) {
                handleStuck(player);
                stuckCounter = 0;
            }
        } else {
            stuckCounter = 0;
        }
        lastPosition = cur;
    }

    private void handleStuck(EntityPlayerSP player) {
        System.out.println("[JasperGoTo] Detectado travamento! Recalculando rota...");
        player.addChatMessage(new ChatComponentText(
            EnumChatFormatting.YELLOW + "[JasperGoTo] " +
            EnumChatFormatting.RED + "⚠ Preso! Recalculando rota..."
        ));

        if (player.onGround) {
            player.jump();
            lastJumpTime = System.currentTimeMillis();
        }

        backwardTicks = BACKWARD_TICKS_ON_STUCK;
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindBack.getKeyCode(), true);

        if (pathfinding.getTarget() != null) {
            BlockPos playerPos = new BlockPos(player.posX, player.posY, player.posZ);

            BlockPos[] alternatives = {
                playerPos.add(1, 0, 0),
                playerPos.add(-1, 0, 0),
                playerPos.add(0, 0, 1),
                playerPos.add(0, 0, -1),
                playerPos.add(0, 1, 0),
                playerPos.add(1, 1, 0),
                playerPos.add(-1, 1, 0),
                playerPos.add(0, 1, 1),
                playerPos.add(0, 1, -1)
            };

            for (BlockPos alt : alternatives) {
                if (isPositionSafe(alt)) {
                    pathfinding.findPath(alt, pathfinding.getTarget());
                    currentWaypointIndex = 0;
                    return;
                }
            }

            pathfinding.findPath(playerPos, pathfinding.getTarget());
            currentWaypointIndex = 0;
        }
    }

    private boolean isPositionSafe(BlockPos pos) {
        if (mc.theWorld == null) return false;
        World w = mc.theWorld;

        Block blockAt = w.getBlockState(pos).getBlock();
        Block blockAbove = w.getBlockState(pos.up()).getBlock();
        Block blockBelow = w.getBlockState(pos.down()).getBlock();

        return blockAt.getMaterial().isReplaceable() &&
               blockAbove.getMaterial().isReplaceable() &&
               (blockBelow.getMaterial().isSolid() || blockBelow.getMaterial() == Material.water);
    }

    private double getDistanceToBlock(EntityPlayerSP player, BlockPos pos) {
        double dx = pos.getX() + 0.5 - player.posX;
        double dy = pos.getY() - player.posY;
        double dz = pos.getZ() + 0.5 - player.posZ;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }

    public void stopMovement() {
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindForward.getKeyCode(), false);
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindSneak.getKeyCode(), false);
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindBack.getKeyCode(), false);
        backwardTicks = 0;

        if (mc.thePlayer != null) {
            mc.thePlayer.setSprinting(false);
        }

        currentWaypointIndex = 0;
        currentTarget = null;
    }

    public void reset() {
        stopMovement();
        stuckCounter = 0;
        lastPosition = null;
        currentWaypointIndex = 0;
        lastJumpPos = null;
        ticksSinceLastJump = 0;
        wasOnGround = true;
        needsJumpNext = false;
    }

    public void onTick() {
        // Backward movement timeout
        if (backwardTicks > 0) {
            backwardTicks--;
            if (backwardTicks == 0) {
                KeyBinding.setKeyBindState(mc.gameSettings.keyBindBack.getKeyCode(), false);
            }
        }
        // Side avoidance timeout
        if (sideAvoidTicks > 0) {
            sideAvoidTicks--;
            if (sideAvoidTicks == 0) {
                KeyBinding.setKeyBindState(mc.gameSettings.keyBindLeft.getKeyCode(), false);
                KeyBinding.setKeyBindState(mc.gameSettings.keyBindRight.getKeyCode(), false);
            }
        }
    }
}