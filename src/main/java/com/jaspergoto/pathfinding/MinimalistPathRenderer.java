package com.jaspergoto.pathfinding;

import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.WorldRenderer;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.BlockPos;
import net.minecraftforge.client.event.RenderWorldLastEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import org.lwjgl.opengl.GL11;

import java.util.List;

public class MinimalistPathRenderer {
    
    private final AdvancedPathfindingEngine pathfinding;
    
    // === CONFIGURAÇÕES VISUAIS MINIMALISTAS ===
    private static final float LINE_WIDTH = 2.0f;
    private static final float WAYPOINT_SIZE = 0.25f;
    private static final float TARGET_SIZE = 0.4f;
    
    // === CORES CLEAN ===
    private static final float[] PATH_COLOR = {1.0f, 0.6f, 0.0f, 0.85f};      // Laranja clean
    private static final float[] WAYPOINT_COLOR = {1.0f, 0.6f, 0.0f, 0.7f};   // Laranja transparente
    private static final float[] TARGET_COLOR = {1.0f, 0.6f, 0.0f, 0.9f};     // Laranja forte
    private static final float[] NEXT_WAYPOINT_COLOR = {1.0f, 1.0f, 1.0f, 0.8f}; // Branco para próximo
    
    public MinimalistPathRenderer(AdvancedPathfindingEngine pathfinding) {
        this.pathfinding = pathfinding;
    }
    
    @SubscribeEvent
    public void onRenderWorldLast(RenderWorldLastEvent event) {
        if (!pathfinding.hasPath()) return;
        
        EntityPlayer player = Minecraft.getMinecraft().thePlayer;
        if (player == null) return;
        
        // Posição interpolada do jogador
        double playerX = player.lastTickPosX + (player.posX - player.lastTickPosX) * event.partialTicks;
        double playerY = player.lastTickPosY + (player.posY - player.lastTickPosY) * event.partialTicks;
        double playerZ = player.lastTickPosZ + (player.posZ - player.lastTickPosZ) * event.partialTicks;
        
        // Setup OpenGL clean
        setupRenderState();
        
        GlStateManager.pushMatrix();
        GlStateManager.translate(-playerX, -playerY, -playerZ);
        
        try {
            List<BlockPos> path = pathfinding.getCurrentPath();
            BlockPos playerPos = new BlockPos(playerX, playerY, playerZ);
            BlockPos nextWaypoint = pathfinding.getNextWaypoint(playerPos);
            
            // 1. Linha do caminho (simples e clean)
            renderCleanPathLine(path);
            
            // 2. Waypoints (caixas wireframe minimalistas)
            renderCleanWaypoints(path, nextWaypoint);
            
            // 3. Marcador de destino (destacado)
            BlockPos target = pathfinding.getTarget();
            if (target != null) {
                renderCleanTargetMarker(target);
            }
            
        } finally {
            GlStateManager.popMatrix();
            restoreRenderState();
        }
    }
    
    // === SETUP OPENGL LIMPO ===
    
    private void setupRenderState() {
        GlStateManager.disableTexture2D();
        GlStateManager.disableLighting();
        GlStateManager.enableBlend();
        GlStateManager.blendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);
        GlStateManager.disableDepth();
        GL11.glEnable(GL11.GL_LINE_SMOOTH);
        GL11.glHint(GL11.GL_LINE_SMOOTH_HINT, GL11.GL_NICEST);
    }
    
    private void restoreRenderState() {
        GL11.glDisable(GL11.GL_LINE_SMOOTH);
        GlStateManager.enableDepth();
        GlStateManager.disableBlend();
        GlStateManager.enableLighting();
        GlStateManager.enableTexture2D();
    }
    
    // === RENDERIZAÇÃO MINIMALISTA ===
    
    private void renderCleanPathLine(List<BlockPos> path) {
        if (path.size() < 2) return;
        
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer worldRenderer = tessellator.getWorldRenderer();
        
        GL11.glLineWidth(LINE_WIDTH);
        worldRenderer.begin(GL11.GL_LINE_STRIP, DefaultVertexFormats.POSITION_COLOR);
        
        for (BlockPos pos : path) {
            worldRenderer.pos(pos.getX() + 0.5, pos.getY() + 0.1, pos.getZ() + 0.5)
                        .color(PATH_COLOR[0], PATH_COLOR[1], PATH_COLOR[2], PATH_COLOR[3])
                        .endVertex();
        }
        
        tessellator.draw();
    }
    
    private void renderCleanWaypoints(List<BlockPos> path, BlockPos nextWaypoint) {
        for (int i = 0; i < path.size(); i++) {
            BlockPos pos = path.get(i);
            
            // Pular alguns waypoints intermediários para manter limpo
            boolean isImportant = (i == 0) || (i == path.size() - 1) || (i % 4 == 0);
            if (!isImportant) continue;
            
            // Destacar próximo waypoint
            boolean isNext = nextWaypoint != null && pos.equals(nextWaypoint);
            float[] color = isNext ? NEXT_WAYPOINT_COLOR : WAYPOINT_COLOR;
            float size = isNext ? WAYPOINT_SIZE * 1.2f : WAYPOINT_SIZE;
            
            renderWireframeCube(pos, size, color);
        }
    }
    
    private void renderCleanTargetMarker(BlockPos target) {
        // Cubo wireframe maior para o destino
        renderWireframeCube(target, TARGET_SIZE, TARGET_COLOR);
        
        // Linha vertical sutil para destacar
        renderVerticalLine(target, TARGET_COLOR);
    }
    
    // === PRIMITIVAS GEOMÉTRICAS CLEAN ===
    
    private void renderWireframeCube(BlockPos pos, float size, float[] color) {
        double x = pos.getX() + 0.5;
        double y = pos.getY() + 0.5;
        double z = pos.getZ() + 0.5;
        
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer worldRenderer = tessellator.getWorldRenderer();
        
        GL11.glLineWidth(1.5f);
        worldRenderer.begin(GL11.GL_LINES, DefaultVertexFormats.POSITION_COLOR);
        
        // === ARESTAS DO CUBO (12 linhas) ===
        
        // Arestas horizontais superiores (4 linhas)
        addLine(worldRenderer, x - size, y + size, z - size, x + size, y + size, z - size, color);
        addLine(worldRenderer, x + size, y + size, z - size, x + size, y + size, z + size, color);
        addLine(worldRenderer, x + size, y + size, z + size, x - size, y + size, z + size, color);
        addLine(worldRenderer, x - size, y + size, z + size, x - size, y + size, z - size, color);
        
        // Arestas horizontais inferiores (4 linhas)
        addLine(worldRenderer, x - size, y - size, z - size, x + size, y - size, z - size, color);
        addLine(worldRenderer, x + size, y - size, z - size, x + size, y - size, z + size, color);
        addLine(worldRenderer, x + size, y - size, z + size, x - size, y - size, z + size, color);
        addLine(worldRenderer, x - size, y - size, z + size, x - size, y - size, z - size, color);
        
        // Arestas verticais (4 linhas)
        addLine(worldRenderer, x - size, y - size, z - size, x - size, y + size, z - size, color);
        addLine(worldRenderer, x + size, y - size, z - size, x + size, y + size, z - size, color);
        addLine(worldRenderer, x + size, y - size, z + size, x + size, y + size, z + size, color);
        addLine(worldRenderer, x - size, y - size, z + size, x - size, y + size, z + size, color);
        
        tessellator.draw();
    }
    
    private void renderVerticalLine(BlockPos pos, float[] color) {
        double x = pos.getX() + 0.5;
        double y = pos.getY();
        double z = pos.getZ() + 0.5;
        
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer worldRenderer = tessellator.getWorldRenderer();
        
        GL11.glLineWidth(1.0f);
        worldRenderer.begin(GL11.GL_LINES, DefaultVertexFormats.POSITION_COLOR);
        
        // Linha sutil para cima
        worldRenderer.pos(x, y, z)
                    .color(color[0], color[1], color[2], color[3] * 0.6f).endVertex();
        worldRenderer.pos(x, y + 15, z)
                    .color(color[0], color[1], color[2], 0.0f).endVertex();
        
        tessellator.draw();
    }
    
    // === UTILITÁRIOS ===
    
    private void addLine(WorldRenderer worldRenderer, double x1, double y1, double z1, 
                        double x2, double y2, double z2, float[] color) {
        worldRenderer.pos(x1, y1, z1).color(color[0], color[1], color[2], color[3]).endVertex();
        worldRenderer.pos(x2, y2, z2).color(color[0], color[1], color[2], color[3]).endVertex();
    }
    
    // === MÉTODOS PÚBLICOS DE CONFIGURAÇÃO ===
    
    /**
     * Alterna entre mostrar todos os waypoints ou apenas os importantes
     */
    public static boolean showAllWaypoints = false;
    
    /**
     * Cor personalizada do caminho (RGBA 0.0-1.0)
     */
    public static void setPathColor(float r, float g, float b, float a) {
        PATH_COLOR[0] = r;
        PATH_COLOR[1] = g;
        PATH_COLOR[2] = b;
        PATH_COLOR[3] = a;
        
        // Ajustar cores relacionadas automaticamente
        WAYPOINT_COLOR[0] = r;
        WAYPOINT_COLOR[1] = g;
        WAYPOINT_COLOR[2] = b;
        WAYPOINT_COLOR[3] = a * 0.8f;
        
        TARGET_COLOR[0] = r;
        TARGET_COLOR[1] = g;
        TARGET_COLOR[2] = b;
        TARGET_COLOR[3] = Math.min(1.0f, a * 1.1f);
    }
    
    /**
     * Presets de cores
     */
    public static void setColorPreset(ColorPreset preset) {
        switch (preset) {
            case ORANGE:
                setPathColor(1.0f, 0.6f, 0.0f, 0.85f);
                break;
            case CYAN:
                setPathColor(0.0f, 1.0f, 1.0f, 0.85f);
                break;
            case GREEN:
                setPathColor(0.0f, 1.0f, 0.5f, 0.85f);
                break;
            case PURPLE:
                setPathColor(0.8f, 0.2f, 1.0f, 0.85f);
                break;
            case WHITE:
                setPathColor(1.0f, 1.0f, 1.0f, 0.85f);
                break;
            case RED:
                setPathColor(1.0f, 0.2f, 0.2f, 0.85f);
                break;
        }
    }
    
    public enum ColorPreset {
        ORANGE, CYAN, GREEN, PURPLE, WHITE, RED
    }
    
    /**
     * Configurações de espessura das linhas
     */
    public static void setLineWidth(float width) {
        // Implementar se necessário - por enquanto usa constante
    }
    
    /**
     * Configurações de tamanho dos waypoints
     */
    public static void setWaypointSize(float size) {
        // Implementar se necessário - por enquanto usa constante
    }
}