package com.jaspergoto.pathfinding;

import net.minecraft.client.Minecraft;
import net.minecraft.util.BlockPos;
import net.minecraft.util.EnumFacing;
import net.minecraft.block.Block;
import net.minecraft.block.material.Material;
import net.minecraft.world.World;

import java.util.*;

public class PathfindingEngine {
    
    private static final int MAX_PATH_LENGTH = 18000; // equilibrado para encontrar caminhos
    private static final int MAX_ITERATIONS = 180000; // aumentado para garantir que encontre caminhos
    private static final int SEARCH_RADIUS = 500; // área de busca ampliada
    private static final double HEURISTIC_WEIGHT = 1.4; // heurística balanceada
    private static final int MAX_RECALC_ATTEMPTS = 3;
    private static final int MAX_NODES_EXPANDED = 45000; // aumentado para garantir busca completa
    // Width of corridor (in blocks) around the straight line start-target to limit search space
    private static final double CORRIDOR_WIDTH = 25.0; // corredor mais amplo para garantir caminhos
    
    // Sistema anti-travamento melhorado
    private int recalcAttempts = 0;
    private long lastRecalcTime = 0;
    private static final long RECALC_COOLDOWN = 1000; // Reduzido para resposta mais rápida
    
    private World world;
    private List<BlockPos> currentPath;
    private List<BlockPos> interpolatedPath;
    private BlockPos targetPos;
    private BlockPos startPos;
    private boolean isCalculating = false;
    
    // Cache para resultados de walkable
    private final Map<BlockPos, Boolean> walkableCache = new HashMap<>();
    
    // Chunk-level terrain analysis cache
    private final Map<String, ChunkTerrainData> chunkAnalysisCache = new HashMap<>();
    
    // Simple cache for repeated queries
    private BlockPos lastStartCache;
    private BlockPos lastTargetCache;
    private List<BlockPos> lastPathCache;
    
    public PathfindingEngine(World world) {
        this.world = world;
        this.currentPath = new ArrayList<>();
        this.interpolatedPath = new ArrayList<>();
    }
    
    // Chunk terrain data structure
    private static class ChunkTerrainData {
        final Set<BlockPos> hazardPositions = new HashSet<>();
        final Set<BlockPos> waterPositions = new HashSet<>();
        final Set<BlockPos> lavaPositions = new HashSet<>();
        final Set<BlockPos> firePositions = new HashSet<>();
        final Set<BlockPos> safePositions = new HashSet<>();
        final Map<BlockPos, Integer> heightMap = new HashMap<>();
        final long analysisTime = System.currentTimeMillis();
        
        boolean isExpired() {
            return System.currentTimeMillis() - analysisTime > 30000; // 30 seconds
        }
    }
    
    public class Node implements Comparable<Node> {
        BlockPos pos;
        Node parent;
        double g;
        double h;
        double f;
        
        public Node(BlockPos pos, Node parent, double g, double h) {
            this.pos = pos;
            this.parent = parent;
            this.g = g;
            this.h = h;
            this.f = g + h;
        }
        
        @Override
        public int compareTo(Node other) {
            int cmp = Double.compare(this.f, other.f);
            if (cmp != 0) return cmp;
            // Tie‑breaker: prefer node with lower g (closer to start)
            return Double.compare(this.g, other.g);
        }
    }
    
    public List<BlockPos> findPath(BlockPos start, BlockPos target) {
        // Return cached path if same start/target as previous call
        if (start.equals(lastStartCache) && target.equals(lastTargetCache) && lastPathCache != null) {
            System.out.println("[JasperGoTo] Using cached path");
            currentPath = new ArrayList<>(lastPathCache);
            interpolatedPath = new ArrayList<>(currentPath);
            return currentPath;
        }
        
        // Early exit if start and target are the same block
        if (start.equals(target)) {
            currentPath = Collections.singletonList(start);
            interpolatedPath = new ArrayList<>(currentPath);
            // Cache the trivial path
            lastStartCache = start;
            lastTargetCache = target;
            lastPathCache = new ArrayList<>(currentPath);
            return currentPath;
        }

        // Sistema anti-travamento
        long currentTime = System.currentTimeMillis();
        if (isCalculating && currentTime - lastRecalcTime < RECALC_COOLDOWN) {
            return currentPath;
        }

        if (isCalculating) {
            recalcAttempts++;
            if (recalcAttempts > MAX_RECALC_ATTEMPTS) {
                System.out.println("[JasperGoTo] Muitas tentativas de recálculo, usando caminho parcial");
                isCalculating = false;
                return currentPath;
            }
        }

        isCalculating = true;
        lastRecalcTime = currentTime;
        
        // Ensure start and target are aligned to ground and walkable
        start = alignToGround(start);
        if (!isWalkableAdvanced(start)) {
            BlockPos s = findNearestSafeGround(start, 8);
            if (s == null) {
                System.out.println("[JasperGoTo] Start sem chão seguro próximo.");
                isCalculating = false;
                return Collections.emptyList();
            }
            start = s;
        }

        target = alignToGround(target);
        if (!isWalkableAdvanced(target)) {
            BlockPos t = findNearestSafeGround(target, 8);
            if (t == null) {
                System.out.println("[JasperGoTo] Target sem chão seguro próximo.");
                isCalculating = false;
                return Collections.emptyList();
            }
            target = t;
        }
        this.startPos = start;
        this.targetPos = target;

        double totalDistance = getDistance(start, target);
        System.out.println("[JasperGoTo] Pathfinding configurado: " + start + " -> " + target +
                         " (distância: " + (int)totalDistance + " blocos)");

        // Abort A* if target is beyond a reasonable search radius (avoid huge searches)
        if (totalDistance > SEARCH_RADIUS * 2) {
            System.out.println("[JasperGoTo] Target muito distante (" + (int)totalDistance +
                             " > " + (SEARCH_RADIUS * 2) + "), usando caminho direto");
            currentPath = createOptimizedDirectPath(start, target);
            currentPath = smoothPath(currentPath);
            createInterpolatedPath(currentPath);
            isCalculating = false;
            recalcAttempts = 0;
            clearWalkableCache();
            // Update cache
            lastStartCache = start;
            lastTargetCache = target;
            lastPathCache = new ArrayList<>(currentPath);
            return currentPath;
        }
        
        // Quick check: if start and target have a clear line of sight, use direct path
        if (hasAdvancedDirectPath(start, target)) {
            System.out.println("[JasperGoTo] Direct line of sight, using optimized direct path");
            currentPath = createOptimizedDirectPath(start, target);
            currentPath = smoothPath(currentPath);
            createInterpolatedPath(currentPath);
            isCalculating = false;
            recalcAttempts = 0;
            clearWalkableCache();
            // Update cache
            lastStartCache = start;
            lastTargetCache = target;
            lastPathCache = new ArrayList<>(currentPath);
            return currentPath;
        }

        // Verificação adicional: tentar caminho semi-direto com poucos waypoints intermediários
        List<BlockPos> semiDirectPath = attemptSemiDirectPath(start, target);
        if (!semiDirectPath.isEmpty()) {
            System.out.println("[JasperGoTo] Semi-direct path found, using optimized route with " + semiDirectPath.size() + " waypoints");
            currentPath = semiDirectPath;
            currentPath = smoothPath(currentPath);
            createInterpolatedPath(currentPath);
            isCalculating = false;
            recalcAttempts = 0;
            clearWalkableCache();
            // Update cache
            lastStartCache = start;
            lastTargetCache = target;
            lastPathCache = new ArrayList<>(currentPath);
            return currentPath;
        } else {
            System.out.println("[JasperGoTo] Semi-direct path failed, using full A* pathfinding");
        }

        // A* Pathfinding melhorado com detecção avançada de obstáculos
        PriorityQueue<Node> openSet = new PriorityQueue<>();
        Set<BlockPos> closedSet = new HashSet<>();
        Map<BlockPos, Double> gScore = new HashMap<>();
        
        Node startNode = new Node(start, null, 0, getHeuristicDistance(start, target));
        openSet.add(startNode);
        gScore.put(start, 0.0);
        
        int iterations = 0;
        int nodesExpanded = 0;
        
        while (!openSet.isEmpty() && iterations < MAX_ITERATIONS && nodesExpanded < MAX_NODES_EXPANDED) {
            iterations++;
            nodesExpanded++;

            Node current = openSet.poll();

            // Chegou ao destino
            if (current.pos.equals(target) || getDistance(current.pos, target) < 1.5) {
                currentPath = reconstructPath(current);
                currentPath = optimizePathAdvanced(currentPath); // adicionar otimização
                currentPath = smoothPath(currentPath);
                createInterpolatedPath(currentPath);
                isCalculating = false;
                System.out.println("[JasperGoTo] Caminho encontrado com " + currentPath.size() + " waypoints em " + iterations + " iterações, distância final: " + getDistance(current.pos, target));
                // Update cache
                lastStartCache = start;
                lastTargetCache = target;
                lastPathCache = new ArrayList<>(currentPath);
                return currentPath;
            }

            // Debug a cada 15000 iterações
            if (iterations % 15000 == 0) {
                System.out.println("[JasperGoTo] A* Progress: " + iterations + " iterações, " +
                                 openSet.size() + " nós abertos, " + closedSet.size() + " nós fechados, distância atual: " +
                                 getDistance(current.pos, target));
            }
            
            closedSet.add(current.pos);
            
            // Verificar vizinhos com detecção avançada de obstáculos
            for (BlockPos neighbor : getAdvancedNeighbors(current.pos)) {
                if (closedSet.contains(neighbor)) continue;
                
                // Limit neighbor generation to within SEARCH_RADIUS of both start and target
                if (getDistance(startPos, neighbor) > SEARCH_RADIUS) continue;
                if (getDistance(targetPos, neighbor) > SEARCH_RADIUS) continue;
                if (!isWalkableAdvanced(neighbor)) continue;
                
                double tentativeG = current.g + getAdvancedMovementCost(current.pos, neighbor);
                
                if (tentativeG > MAX_PATH_LENGTH) continue;
                
                if (!gScore.containsKey(neighbor) || tentativeG < gScore.get(neighbor)) {
                    gScore.put(neighbor, tentativeG);
                    
                    Node neighborNode = new Node(
                        neighbor,
                        current,
                        tentativeG,
                        getHeuristicDistance(neighbor, target)
                    );
                    
                    openSet.add(neighborNode);
                }
            }
        }
        
        // Fallback inteligente (or node limit reached)
        if (!openSet.isEmpty()) {
            Node bestNode = null;
            double bestDistance = Double.MAX_VALUE;

            for (Node node : openSet) {
                double distance = getDistance(node.pos, target);
                if (distance < bestDistance) {
                    bestDistance = distance;
                    bestNode = node;
                }
            }

            if (bestNode != null) {
                currentPath = reconstructPath(bestNode);
                currentPath = smoothPath(currentPath);
                createInterpolatedPath(currentPath);
                System.out.println("[JasperGoTo] Caminho parcial encontrado - distância restante: " + (int)bestDistance);
                // Update cache
                lastStartCache = start;
                lastTargetCache = target;
                lastPathCache = new ArrayList<>(currentPath);
            }
        } else {
            // Última tentativa: tentar caminho direto como fallback
            System.out.println("[JasperGoTo] A* falhou completamente. Tentando caminho direto como fallback...");
            currentPath = createOptimizedDirectPath(start, target);
            if (currentPath.isEmpty()) {
                System.out.println("[JasperGoTo] Nenhum caminho seguro encontrado, incluindo direto.");
            } else {
                System.out.println("[JasperGoTo] Caminho direto de fallback criado com " + currentPath.size() + " waypoints");
                createInterpolatedPath(currentPath);
            }
            lastStartCache = start;
            lastTargetCache = target;
            lastPathCache = new ArrayList<>(currentPath);
        }

        isCalculating = false;
        recalcAttempts = 0;
        clearWalkableCache();
        return currentPath;
    }
    
    private List<BlockPos> reconstructPath(Node node) {
        List<BlockPos> path = new ArrayList<>();
        Node current = node;
        
        while (current != null) {
            path.add(0, current.pos);
            current = current.parent;
        }
        
        return optimizePathAdvanced(path);
    }
    
    // Sistema ultra otimizado de caminho para trajetos mais diretos
    private List<BlockPos> optimizePathAdvanced(List<BlockPos> path) {
        if (path.size() <= 2) return path;

        List<BlockPos> optimized = new ArrayList<>();
        optimized.add(path.get(0));

        int i = 0;
        while (i < path.size() - 1) {
            int bestJ = i + 1;

            // Tentar pular waypoints usando line-of-sight avançado - mais agressivo
            for (int j = i + 2; j < path.size(); j++) {
                if (hasAdvancedDirectPath(path.get(i), path.get(j))) {
                    bestJ = j;
                } else {
                    break;
                }
            }

            // Adicionar waypoint intermediário apenas se a distância for muito grande
            if (bestJ > i + 1) {
                double distance = getDistance(path.get(i), path.get(bestJ));
                if (distance > 12.0) { // aumentado de 8.0 para 12.0 para menos waypoints
                    int midPoint = (i + bestJ) / 2;
                    optimized.add(path.get(midPoint));
                    i = midPoint;
                    continue;
                }
            }

            optimized.add(path.get(bestJ));
            i = bestJ;
        }

        // Segunda passada: otimização adicional para remover waypoints redundantes
        return optimizeRedundantWaypoints(optimized);
    }

    // Remove waypoints redundantes que estão muito próximos ou em linha reta
    private List<BlockPos> optimizeRedundantWaypoints(List<BlockPos> path) {
        if (path.size() <= 2) return path;

        List<BlockPos> result = new ArrayList<>();
        result.add(path.get(0));

        for (int i = 1; i < path.size() - 1; i++) {
            BlockPos prev = result.get(result.size() - 1);
            BlockPos current = path.get(i);
            BlockPos next = path.get(i + 1);

            // Verificar se o waypoint atual é necessário
            if (!hasAdvancedDirectPath(prev, next) || getDistance(prev, current) > 6.0) {
                result.add(current);
            }
        }

        result.add(path.get(path.size() - 1));
        return result;
    }
    
    // Criar caminho interpolado otimizado para movimento fluido
    private void createInterpolatedPath(List<BlockPos> path) {
        interpolatedPath.clear();
        if (path.isEmpty()) return;

        for (int i = 0; i < path.size() - 1; i++) {
            BlockPos from = path.get(i);
            BlockPos to = path.get(i + 1);

            interpolatedPath.add(from);

            // Interpolação otimizada - densidade baseada na distância
            double distance = getDistance(from, to);
            int steps = Math.max(1, (int)(distance * 4)); // densidade reduzida para movimento mais fluido

            // Só interpolar se a distância for significativa
            if (distance > 1.5) {
                for (int step = 1; step < steps; step++) {
                    double t = (double) step / steps;
                    int x = (int) Math.round(from.getX() + (to.getX() - from.getX()) * t);
                    int y = (int) Math.round(from.getY() + (to.getY() - from.getY()) * t);
                    int z = (int) Math.round(from.getZ() + (to.getZ() - from.getZ()) * t);

                    // Align to ground to guarantee grounded interpolation samples
                    BlockPos interpolated = alignToGround(new BlockPos(x, y, z));
                    // Use advanced validation to avoid waypoints "flying" sobre água/lava
                    if (isWalkableAdvanced(interpolated) && isSegmentSampleGrounded(interpolated)) {
                        // Evitar waypoints muito próximos
                        if (interpolatedPath.isEmpty() ||
                            getDistance(interpolatedPath.get(interpolatedPath.size() - 1), interpolated) > 0.8) {
                            interpolatedPath.add(interpolated);
                        }
                    }
                }
            }
        }

        if (!path.isEmpty()) {
            interpolatedPath.add(path.get(path.size() - 1));
        }
    }

    // Align a position to the ground: descend until solid support, then move up to standable air
    private BlockPos alignToGround(BlockPos pos) {
        BlockPos current = pos;

        // Desce até encontrar um bloco sólido
        while (world.isAirBlock(current.down()) && current.getY() > 0) {
            current = current.down();
        }

        // Sobe se estiver dentro de bloco sólido
        while (!world.isAirBlock(current) && current.getY() < world.getHeight()) {
            current = current.up();
        }

        return current;
    }
    
    // Advanced path smoothing: remove unnecessary points using line-of-sight checks
    private List<BlockPos> smoothPath(List<BlockPos> path) {
        if (path.size() <= 2) return path;
        List<BlockPos> smoothed = new ArrayList<>();
        int startIdx = 0;
        smoothed.add(path.get(startIdx));
        while (startIdx < path.size() - 1) {
            int furthest = startIdx + 1;
            for (int i = path.size() - 1; i > startIdx; i--) {
                if (hasAdvancedDirectPath(path.get(startIdx), path.get(i))) {
                    furthest = i;
                    break;
                }
            }
            smoothed.add(path.get(furthest));
            startIdx = furthest;
        }
        return smoothed;
    }
    
    // Detecção avançada de vizinhos com mais opções de movimento
    private List<BlockPos> getAdvancedNeighbors(BlockPos pos) {
        List<BlockPos> neighbors = new ArrayList<>();
        
        // Enhanced 3D movement directions including more vertical options
        int[] dx = {0, 0, 1, -1, 1, -1, 1, -1};
        int[] dz = {1, -1, 0, 0, 1, 1, -1, -1};

        for (int i = 0; i < 8; i++) {
            int deltaX = dx[i];
            int deltaZ = dz[i];

            // 1. Horizontal movement at same level
            addNeighborIfValid(neighbors, pos.add(deltaX, 0, deltaZ), pos);
            
            // 2. Upward movement (jumping) - try multiple heights
            for (int jumpHeight = 1; jumpHeight <= 3; jumpHeight++) {
                BlockPos upPos = pos.add(deltaX, jumpHeight, deltaZ);
                if (canJumpToAdvanced(pos, upPos)) {
                    addNeighborIfValid(neighbors, upPos, pos);
                }
            }
            
            // 3. Downward movement (falling/stepping down) - try multiple depths
            for (int dropHeight = 1; dropHeight <= 5; dropHeight++) {
                BlockPos downPos = pos.add(deltaX, -dropHeight, deltaZ);
                if (canDropToAdvanced(pos, downPos)) {
                    addNeighborIfValid(neighbors, downPos, pos);
                    break; // Only add the first valid drop level
                }
            }
            
            // 4. Stair-like movement (up 1, forward 1 - common in terrain)
            BlockPos stairUp = pos.add(deltaX, 1, deltaZ);
            if (canStepUpTo(pos, stairUp)) {
                addNeighborIfValid(neighbors, stairUp, pos);
            }
            
            // 5. Slope movement (down 1, forward 1 - for slopes)
            BlockPos slopeDown = pos.add(deltaX, -1, deltaZ);
            if (canStepDownTo(pos, slopeDown)) {
                addNeighborIfValid(neighbors, slopeDown, pos);
            }
        }
        
        // 6. Pure vertical movement (climbing/falling straight up/down)
        for (int dy = 1; dy <= 2; dy++) {
            BlockPos up = pos.add(0, dy, 0);
            if (canClimbUp(pos, up)) {
                addNeighborIfValid(neighbors, up, pos);
            }
        }
        
        for (int dy = 1; dy <= 3; dy++) {
            BlockPos down = pos.add(0, -dy, 0);
            if (canFallDown(pos, down)) {
                addNeighborIfValid(neighbors, down, pos);
                break; // Only add first valid fall level
            }
        }
        
        return neighbors;
    }
    
    // Helper method to add neighbor if it passes all validation checks
    private void addNeighborIfValid(List<BlockPos> neighbors, BlockPos neighbor, BlockPos from) {
        // Ground-align the candidate first to avoid floating Y errors
        BlockPos grounded = alignToGround(neighbor);
        if (getDistance(startPos, grounded) <= SEARCH_RADIUS &&
            isWithinCorridor(grounded) &&
            isWalkableAdvanced(grounded)) {
            neighbors.add(grounded);
        }
    }
    
    // Check if we can step up to a position (like stairs)
    private boolean canStepUpTo(BlockPos from, BlockPos to) {
        int heightDiff = to.getY() - from.getY();
        if (heightDiff != 1) return false;
        
        // Check if there's headroom above the destination
        return isFreeSpace(to.up()) && isFreeSpace(to.up(2));
    }
    
    // Check if we can step down to a position (like slopes)
    private boolean canStepDownTo(BlockPos from, BlockPos to) {
        int heightDiff = from.getY() - to.getY();
        if (heightDiff != 1) return false;
        
        // Simple step down - just check if destination is walkable
        return true; // isWalkableAdvanced already checked in addNeighborIfValid
    }
    
    // Check if we can climb straight up (like ladders or blocks)
    private boolean canClimbUp(BlockPos from, BlockPos to) {
        int heightDiff = to.getY() - from.getY();
        if (heightDiff < 1 || heightDiff > 2) return false;
        
        // Check if there's something to climb (adjacent solid block)
        BlockPos[] adjacents = {from.north(), from.south(), from.east(), from.west()};
        for (BlockPos adj : adjacents) {
            if (world.getBlockState(adj).getBlock().getMaterial().isSolid()) {
                return true; // Can climb using adjacent block
            }
        }
        return false;
    }
    
    // Check if we can fall straight down
    private boolean canFallDown(BlockPos from, BlockPos to) {
        int heightDiff = from.getY() - to.getY();
        if (heightDiff < 1 || heightDiff > 3) return false;
        
        // Check that the fall path is clear
        for (int y = from.getY() - 1; y > to.getY(); y--) {
            BlockPos checkPos = new BlockPos(from.getX(), y, from.getZ());
            if (!isFreeSpace(checkPos)) {
                return false; // Path blocked
            }
        }
        return true;
    }
    
    // Detecção MUITO avançada de terreno caminhável
    private boolean isWalkableAdvanced(BlockPos pos) {
        // Use cache to avoid recomputation
        Boolean cached = walkableCache.get(pos);
        if (cached != null) return cached;

        if (pos.getY() < 0 || pos.getY() > 255) {
            walkableCache.put(pos, false);
            return false;
        }
        if (!world.isBlockLoaded(pos)) {
            walkableCache.put(pos, false);
            return false;
        }

        // Strict immediate-space checks: free space for feet and head, and solid support just below
        if (!world.isAirBlock(pos)) { // espaço atual livre
            walkableCache.put(pos, false);
            return false;
        }
        if (!world.isAirBlock(pos.up())) { // cabeça livre
            walkableCache.put(pos, false);
            return false;
        }

        BlockPos below = pos.down();
        Block blockBelow = world.getBlockState(below).getBlock();
        if (!blockBelow.isSideSolid(world, below, EnumFacing.UP)) {
            walkableCache.put(pos, false);
            return false; // precisa ter bloco sólido logo abaixo
        }

        // COMPREHENSIVE 3D TERRAIN ANALYSIS
        boolean result = is3DTerrainSafe(pos);
        walkableCache.put(pos, result);
        return result;
    }

    // Advanced 3D terrain safety analysis
    private boolean is3DTerrainSafe(BlockPos pos) {
        // 1. Check ground support (must have solid ground)
        BlockPos ground = pos.down();
        Block groundBlock = world.getBlockState(ground).getBlock();
        Material groundMaterial = groundBlock.getMaterial();
        
        // STRICT: Must have solid ground - NO water, lava, or air
        if (!groundMaterial.isSolid()) {
            return false;
        }
        
        // 2. STRICT hazard avoidance - completely forbid dangerous materials
        if (isDangerousMaterial(groundMaterial)) {
            return false;
        }
        
        // 3. Check the position itself and above for free space (2 blocks height)
        if (!isFreeSpace(pos) || !isFreeSpace(pos.up())) {
            return false;
        }
        
        // 4. Check surrounding area for hazards (3x3 area around position)
        if (hasSurroundingHazards(pos)) {
            return false;
        }
        
        // 5. Check for unstable terrain (floating blocks, etc.)
        if (isUnstableTerrain(pos)) {
            return false;
        }
        
        return true;
    }
    
    // Check if material is dangerous and should be completely avoided
    private boolean isDangerousMaterial(Material material) {
        return material == Material.lava ||
               material == Material.water ||  // STRICT: avoid water completely
               material == Material.fire ||
               material == Material.cactus;
    }
    
    // Check if a position has free space (not solid, not liquid, not hazardous)
    private boolean isFreeSpace(BlockPos pos) {
        Block block = world.getBlockState(pos).getBlock();
        Material material = block.getMaterial();
        
        // Must be air or non-solid, non-hazardous material
        if (material.isSolid()) return false;
        if (isDangerousMaterial(material)) return false;
        
        return true;
    }
    
    // Check 3x3 area around position for hazards
    private boolean hasSurroundingHazards(BlockPos center) {
        for (int dx = -1; dx <= 1; dx++) {
            for (int dz = -1; dz <= 1; dz++) {
                BlockPos check = center.add(dx, 0, dz);
                BlockPos checkGround = check.down();
                
                Block groundBlock = world.getBlockState(checkGround).getBlock();
                Material groundMaterial = groundBlock.getMaterial();
                
                // If any surrounding ground is dangerous, avoid this position
                if (isDangerousMaterial(groundMaterial)) {
                    return true;
                }
                
                // Also check the position itself for hazards
                Block checkBlock = world.getBlockState(check).getBlock();
                if (isDangerousMaterial(checkBlock.getMaterial())) {
                    return true;
                }
            }
        }
        return false;
    }
    
    // Check for unstable terrain (like floating sand/gravel)
    private boolean isUnstableTerrain(BlockPos pos) {
        BlockPos ground = pos.down();
        Block groundBlock = world.getBlockState(ground).getBlock();
        
        // Check if ground block is gravity-affected and unsupported
        if (groundBlock.getMaterial() == Material.sand) {
            // Check if sand has support below it
            BlockPos support = ground.down();
            Block supportBlock = world.getBlockState(support).getBlock();
            if (!supportBlock.getMaterial().isSolid()) {
                return true; // Floating sand - unstable
            }
        }
        
        return false;
    }
    
    // Verificação avançada de pulo
    private boolean canJumpToAdvanced(BlockPos from, BlockPos to) {
        int heightDiff = to.getY() - from.getY();
        if (heightDiff > 2 || heightDiff <= 0) return false;

        // Verificar espaço para pular
        if (!world.isAirBlock(from.up(2))) return false;

        // Verificar distância horizontal
        double horizontalDist = Math.sqrt(
            Math.pow(to.getX() - from.getX(), 2) + 
            Math.pow(to.getZ() - from.getZ(), 2)
        );
        
        return horizontalDist <= 4.0;
    }

    // Verificação avançada de queda
    private boolean canDropToAdvanced(BlockPos from, BlockPos to) {
        int heightDiff = from.getY() - to.getY();
        if (heightDiff <= 0 || heightDiff > 4) return false;

        // Verificar se há chão sólido no destino
        if (world.isAirBlock(to.down())) return false;

        // Verificar caminho livre para queda
        for (int y = from.getY(); y > to.getY(); y--) {
            BlockPos checkPos = new BlockPos(to.getX(), y, to.getZ());
            if (!world.isAirBlock(checkPos)) return false;
        }

        return true;
    }
    
    // Verificação balanceada de linha de visão
    private boolean hasAdvancedDirectPath(BlockPos from, BlockPos to) {
        // Segment must remain on safe ground the whole way and never cruzar água/lava
        double distance = getDistance(from, to);
        if (distance > 40) return false; // limite de distância para line of sight

        int steps = Math.max(1, (int)(distance * 1.2)); // verificações balanceadas

        int prevSurfaceY = Integer.MIN_VALUE;
        for (int i = 1; i <= steps; i++) {
            double ratio = (double) i / steps;
            int x = (int) Math.round(from.getX() + (to.getX() - from.getX()) * ratio);
            int y = (int) Math.round(from.getY() + (to.getY() - from.getY()) * ratio);
            int z = (int) Math.round(from.getZ() + (to.getZ() - from.getZ()) * ratio);

            BlockPos sample = new BlockPos(x, y, z);
            // 1) Precisa ser caminhável segundo análise avançada
            if (!isWalkableAdvanced(sample)) {
                return false;
            }
            // 2) Garantir que estamos no chão (não voando) - mais tolerante
            int surfaceY = getSurfaceY(sample);
            if (surfaceY == -1) continue; // pular amostras problemáticas ao invés de falhar
            int expectedGroundY = surfaceY + 1; // posição de pé
            if (Math.abs(sample.getY() - expectedGroundY) > 3) { // ainda mais tolerante
                return false; // permite variações maiores de altura
            }
            // 3) Evitar degraus impossíveis entre amostras consecutivas - mais flexível
            if (prevSurfaceY != Integer.MIN_VALUE) {
                if (Math.abs((surfaceY + 1) - prevSurfaceY) > 3) { // mais tolerante
                    return false; // permite degraus ainda maiores
                }
            }
            prevSurfaceY = expectedGroundY;
        }
        return true;
    }
    
    // Custo de movimento balanceado
    private double getAdvancedMovementCost(BlockPos from, BlockPos to) {
        double baseCost = getDistance(from, to);

        // Penalizar mudanças de altura de forma balanceada
        int heightDiff = Math.abs(to.getY() - from.getY());
        if (heightDiff > 0) {
            baseCost += heightDiff * 0.6; // balanceado
        }

        // Bonus suave para movimento em linha reta em direção ao target
        if (targetPos != null) {
            double directionToTarget = getDirectionAlignment(from, to, targetPos);
            baseCost *= (1.0 - directionToTarget * 0.15); // bonus reduzido para não ser muito restritivo
        }

        // Penalizar materiais difíceis de forma balanceada
        Block groundBlock = world.getBlockState(to.down()).getBlock();
        Material groundMaterial = groundBlock.getMaterial();

        if (groundMaterial == Material.sand) {
            baseCost *= 1.15; // penalidade menor
        } else if (groundMaterial == Material.ice) {
            baseCost *= 0.9; // bonus menor
        } else if (groundMaterial == Material.grass) {
            baseCost *= 1.0; // neutro
        } else if (groundMaterial == Material.rock) {
            baseCost *= 1.02; // penalidade muito pequena
        }

        // Penalizar movimento diagonal de forma suave
        if (Math.abs(to.getX() - from.getX()) > 0 && Math.abs(to.getZ() - from.getZ()) > 0) {
            baseCost *= 1.05; // penalidade muito pequena
        }

        return baseCost;
    }

    // Calcula o alinhamento da direção com o target (0 = perpendicular, 1 = alinhado)
    private double getDirectionAlignment(BlockPos from, BlockPos to, BlockPos target) {
        if (target == null) return 0;

        // Vetor do movimento atual
        double moveX = to.getX() - from.getX();
        double moveZ = to.getZ() - from.getZ();
        double moveLength = Math.sqrt(moveX * moveX + moveZ * moveZ);

        // Vetor direção para o target
        double targetX = target.getX() - from.getX();
        double targetZ = target.getZ() - from.getZ();
        double targetLength = Math.sqrt(targetX * targetX + targetZ * targetZ);

        if (moveLength == 0 || targetLength == 0) return 0;

        // Produto escalar normalizado (coseno do ângulo)
        double dotProduct = (moveX * targetX + moveZ * targetZ) / (moveLength * targetLength);
        return Math.max(0, dotProduct); // apenas valores positivos (mesmo sentido)
    }

    // Heurística balanceada para encontrar caminhos
    private double getHeuristicDistance(BlockPos from, BlockPos to) {
        double dx = Math.abs(to.getX() - from.getX());
        double dy = Math.abs(to.getY() - from.getY());
        double dz = Math.abs(to.getZ() - from.getZ());

        // Distância euclidiana com penalidade vertical balanceada
        double horizontalDistance = Math.sqrt(dx * dx + dz * dz);
        double verticalPenalty = dy * 1.0; // balanceado para permitir variação vertical

        // Bonus suave para caminhos que seguem a linha direta start-target
        if (startPos != null && targetPos != null) {
            double directDistance = getDistance(startPos, targetPos);
            double currentToTarget = getDistance(from, targetPos);
            double currentToStart = getDistance(from, startPos);
            double linearity = Math.abs((currentToStart + currentToTarget) - directDistance);
            double linearityPenalty = linearity * 0.1; // penalidade muito reduzida
            return (horizontalDistance + verticalPenalty + linearityPenalty) * HEURISTIC_WEIGHT;
        }

        return (horizontalDistance + verticalPenalty) * HEURISTIC_WEIGHT;
    }

    // Caminho direto otimizado
    private List<BlockPos> createDirectPath(BlockPos start, BlockPos target) {
        return createOptimizedDirectPath(start, target);
    }

    // Caminho direto ultra otimizado para movimento mais fluido
    private List<BlockPos> createOptimizedDirectPath(BlockPos start, BlockPos target) {
        List<BlockPos> directPath = new ArrayList<>();

        // Add start only if safe and aligned to ground
        BlockPos alignedStart = alignToGround(start);
        if (isWalkableAdvanced(alignedStart)) {
            directPath.add(alignedStart);
        }

        // Usar menos waypoints para movimento mais fluido
        double distance = getDistance(start, target);
        int steps = Math.max(3, (int)(distance / 8)); // menos waypoints, mais espaçados

        for (int i = 1; i < steps; i++) {
            double progress = (double) i / steps;
            int x = (int) (start.getX() + (target.getX() - start.getX()) * progress);
            int y = (int) (start.getY() + (target.getY() - start.getY()) * progress);
            int z = (int) (start.getZ() + (target.getZ() - start.getZ()) * progress);

            BlockPos candidate = alignToGround(new BlockPos(x, y, z));
            if (isWalkableAdvanced(candidate)) {
                // ensure we don't add duplicates and maintain minimum distance
                if (directPath.isEmpty() ||
                    getDistance(directPath.get(directPath.size()-1), candidate) > 2.0) {
                    directPath.add(candidate);
                }
            }
        }

        BlockPos alignedTarget = alignToGround(target);
        if (isWalkableAdvanced(alignedTarget)) {
            directPath.add(alignedTarget);
        } else {
            BlockPos nearTarget = findNearestSafeGround(alignedTarget, 6);
            if (nearTarget != null && isWalkableAdvanced(nearTarget)) {
                directPath.add(nearTarget);
            }
        }
        return directPath;
    }

    // Tenta encontrar um caminho semi-direto com poucos waypoints intermediários
    private List<BlockPos> attemptSemiDirectPath(BlockPos start, BlockPos target) {
        List<BlockPos> semiPath = new ArrayList<>();

        // Dividir o caminho em 3-5 segmentos e tentar encontrar waypoints intermediários seguros
        double distance = getDistance(start, target);
        if (distance < 30) return semiPath; // só tentar para distâncias maiores

        int segments = Math.min(4, Math.max(3, (int)(distance / 20))); // menos segmentos
        List<BlockPos> candidates = new ArrayList<>();
        candidates.add(start);

        for (int i = 1; i < segments; i++) {
            double progress = (double) i / segments;
            int x = (int) (start.getX() + (target.getX() - start.getX()) * progress);
            int y = (int) (start.getY() + (target.getY() - start.getY()) * progress);
            int z = (int) (start.getZ() + (target.getZ() - start.getZ()) * progress);

            BlockPos candidate = findNearestSafeGround(new BlockPos(x, y, z), 12); // raio maior
            if (candidate != null) {
                candidates.add(candidate);
            } else {
                return semiPath; // falhou, usar A* completo
            }
        }

        candidates.add(target);

        // Verificar se todos os segmentos são caminháveis - mais tolerante
        for (int i = 0; i < candidates.size() - 1; i++) {
            double segmentDistance = getDistance(candidates.get(i), candidates.get(i + 1));
            if (segmentDistance > 50 || !hasAdvancedDirectPath(candidates.get(i), candidates.get(i + 1))) {
                return semiPath; // falhou, usar A* completo
            }
        }

        System.out.println("[JasperGoTo] Semi-direct path found with " + candidates.size() + " waypoints");
        return candidates;
    }

    // Encontrar chão seguro
    private BlockPos findSafeGround(BlockPos pos) {
        for (int y = pos.getY() + 5; y >= pos.getY() - 15; y--) {
            BlockPos testPos = alignToGround(new BlockPos(pos.getX(), y, pos.getZ()));
            if (isWalkableAdvanced(testPos)) {
                return testPos;
            }
        }
        return null; // do not return the original unsafe position
    }

    // Busca o chão seguro mais próximo em torno de uma posição (expande em raio XZ)
    private BlockPos findNearestSafeGround(BlockPos center, int radius) {
        // primeiro tenta na própria coluna
        BlockPos direct = findSafeGround(center);
        if (direct != null) return direct;
        // explorar em anéis ao redor
        for (int r = 1; r <= radius; r++) {
            for (int dx = -r; dx <= r; dx++) {
                int dz = r;
                BlockPos p1 = findSafeGround(new BlockPos(center.getX() + dx, center.getY(), center.getZ() + dz));
                if (p1 != null) return p1;
                BlockPos p2 = findSafeGround(new BlockPos(center.getX() + dx, center.getY(), center.getZ() - dz));
                if (p2 != null) return p2;
            }
            for (int dz = -r; dz <= r; dz++) {
                int dx = r;
                BlockPos p3 = findSafeGround(new BlockPos(center.getX() + dx, center.getY(), center.getZ() + dz));
                if (p3 != null) return p3;
                BlockPos p4 = findSafeGround(new BlockPos(center.getX() - dx, center.getY(), center.getZ() + dz));
                if (p4 != null) return p4;
            }
        }
        return null;
    }
    
    private double getDistance(BlockPos from, BlockPos to) {
        double dx = to.getX() - from.getX();
        double dy = to.getY() - from.getY();
        double dz = to.getZ() - from.getZ();
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    // Métodos públicos
    public List<BlockPos> getCurrentPath() {
        return currentPath;
    }
    
    public List<BlockPos> getInterpolatedPath() {
        return interpolatedPath.isEmpty() ? currentPath : interpolatedPath;
    }
    
    public void clearPath() {
        currentPath.clear();
        interpolatedPath.clear();
        targetPos = null;
        startPos = null;
    }
    
    public boolean hasPath() {
        return !currentPath.isEmpty();
    }
    
    public BlockPos getTarget() {
        return targetPos;
    }
    
    public boolean isCalculating() {
        return isCalculating;
    }
    
    // Clear walkable cache after each full path calculation to keep memory usage bounded
    private void clearWalkableCache() {
        walkableCache.clear();
        // Also clear chunk-level analysis cache to avoid stale terrain data
        clearChunkAnalysisCache();
    }
    
    // Helper: check if a position lies within the corridor around the straight line start-target
    private boolean isWithinCorridor(BlockPos pos) {
        // Compute perpendicular distance from pos to line segment startPos-targetPos
        if (startPos == null || targetPos == null) return true; // fallback
        double lineDist = distancePointToSegment(startPos, targetPos, pos);
        return lineDist <= CORRIDOR_WIDTH;
    }

    // Euclidean distance from point C to line segment AB
    private double distancePointToSegment(BlockPos a, BlockPos b, BlockPos c) {
        double ax = a.getX();
        double ay = a.getY();
        double az = a.getZ();
        double bx = b.getX();
        double by = b.getY();
        double bz = b.getZ();
        double cx = c.getX();
        double cy = c.getY();
        double cz = c.getZ();

        double abx = bx - ax;
        double aby = by - ay;
        double abz = bz - az;
        double acx = cx - ax;
        double acy = cy - ay;
        double acz = cz - az;

        double abLenSq = abx * abx + aby * aby + abz * abz;
        if (abLenSq == 0) return Math.sqrt(acx * acx + acy * acy + acz * acz);
        double t = (acx * abx + acy * aby + acz * abz) / abLenSq;
        t = Math.max(0, Math.min(1, t));
        double projX = ax + t * abx;
        double projY = ay + t * aby;
        double projZ = az + t * abz;
        double dx = cx - projX;
        double dy = cy - projY;
        double dz = cz - projZ;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }

    // CHUNK-LEVEL TERRAIN ANALYSIS SYSTEM
    
    // Get chunk key for caching
    private String getChunkKey(BlockPos pos) {
        int chunkX = pos.getX() >> 4;
        int chunkZ = pos.getZ() >> 4;
        return chunkX + "," + chunkZ;
    }
    
    // Analyze entire chunk for terrain hazards and features
    private ChunkTerrainData analyzeChunk(BlockPos pos) {
        String chunkKey = getChunkKey(pos);
        
        // Check cache first
        ChunkTerrainData cached = chunkAnalysisCache.get(chunkKey);
        if (cached != null && !cached.isExpired()) {
            return cached;
        }
        
        System.out.println("[JasperGoTo] Analyzing chunk terrain at " + chunkKey);
        
        ChunkTerrainData data = new ChunkTerrainData();
        
        // Get chunk boundaries
        int chunkX = pos.getX() >> 4;
        int chunkZ = pos.getZ() >> 4;
        int startX = chunkX << 4;
        int startZ = chunkZ << 4;
        int endX = startX + 16;
        int endZ = startZ + 16;
        
        // Scan entire chunk from bedrock to sky
        for (int x = startX; x < endX; x++) {
            for (int z = startZ; z < endZ; z++) {
                analyzeChunkColumn(data, x, z);
            }
        }
        
        // Cache the analysis
        chunkAnalysisCache.put(chunkKey, data);
        
        System.out.println("[JasperGoTo] Chunk analysis complete: " + 
                          data.hazardPositions.size() + " hazards, " +
                          data.safePositions.size() + " safe positions");
        
        return data;
    }
    
    // Analyze a single column (x,z) from bedrock to sky
    private void analyzeChunkColumn(ChunkTerrainData data, int x, int z) {
        int surfaceY = -1;
        boolean foundSurface = false;
        
        // Scan from top to bottom to find surface and analyze terrain
        for (int y = 255; y >= 0; y--) {
            BlockPos pos = new BlockPos(x, y, z);
            
            if (!world.isBlockLoaded(pos)) continue;
            
            Block block = world.getBlockState(pos).getBlock();
            Material material = block.getMaterial();
            
            // Find surface level (first solid block from top)
            if (!foundSurface && material.isSolid()) {
                surfaceY = y;
                foundSurface = true;
                data.heightMap.put(new BlockPos(x, 0, z), surfaceY);
            }
            
            // Categorize blocks by material
            if (material == Material.water) {
                data.waterPositions.add(pos);
                data.hazardPositions.add(pos);
            } else if (material == Material.lava) {
                data.lavaPositions.add(pos);
                data.hazardPositions.add(pos);
            } else if (material == Material.fire) {
                data.firePositions.add(pos);
                data.hazardPositions.add(pos);
            } else if (material == Material.cactus) {
                data.hazardPositions.add(pos);
            } else if (material.isSolid() && isSafeMaterial(material)) {
                // Only mark as safe if it's near surface level (within 10 blocks)
                if (foundSurface && Math.abs(y - surfaceY) <= 10) {
                    data.safePositions.add(pos);
                }
            }
        }
    }
    
    // Check if material is safe for walking
    private boolean isSafeMaterial(Material material) {
        return material == Material.rock ||
               material == Material.grass ||
               material == Material.ground ||
               material == Material.sand ||
               material == Material.ice ||
               material == Material.snow ||
               material == Material.wood ||
               material == Material.leaves;
    }
    
    // Enhanced walkable check using chunk analysis
    private boolean isWalkableWithChunkAnalysis(BlockPos pos) {
        // Get chunk analysis
        ChunkTerrainData chunkData = analyzeChunk(pos);
        
        // Quick check: if position is in hazard list, it's not walkable
        if (chunkData.hazardPositions.contains(pos)) {
            return false;
        }
        
        // Check hazards: allow walking on solid structures over deep hazards,
        // but forbid if hazard is in the same column and close below, or very close horizontally.
        int px = pos.getX();
        int pz = pos.getZ();
        for (BlockPos hazard : chunkData.hazardPositions) {
            // Same (x,z) column: reject only if the hazard is within 2 blocks below or above this Y
            if (hazard.getX() == px && hazard.getZ() == pz) {
                int dy = pos.getY() - hazard.getY();
                if (dy <= 2) {
                    return false; // standing at or just above a hazard in the same column
                }
            }
            // Horizontal proximity (ignore Y), keep a tighter 2.0 block buffer to allow riverbank hugging
            double dx = hazard.getX() - px;
            double dz = hazard.getZ() - pz;
            double horiz = Math.sqrt(dx * dx + dz * dz);
            if (horiz <= 2.0) {
                return false; // too close horizontally to hazard
            }
        }
        
        // Use original detailed analysis for final validation
        return is3DTerrainSafe(pos);
    }
    
    // Get terrain safety score for a position based on chunk analysis
    private double getTerrainSafetyScore(BlockPos pos) {
        ChunkTerrainData chunkData = analyzeChunk(pos);
        
        double score = 1.0; // Base safety score
        
        // Penalize based on proximity to hazards
        for (BlockPos hazard : chunkData.hazardPositions) {
            double distance = getDistance(pos, hazard);
            if (distance <= 5.0) {
                score -= (5.0 - distance) / 5.0 * 0.5; // Up to 50% penalty
            }
        }
        
        // Bonus for being near safe positions
        for (BlockPos safe : chunkData.safePositions) {
            double distance = getDistance(pos, safe);
            if (distance <= 3.0) {
                score += (3.0 - distance) / 3.0 * 0.2; // Up to 20% bonus
            }
        }
        
        return Math.max(0.0, Math.min(1.0, score)); // Clamp to [0,1]
    }
    
    // Clear chunk analysis cache
    private void clearChunkAnalysisCache() {
        chunkAnalysisCache.clear();
    }

    // ==== HELPERS para manter segmentos colados ao chão e longe de água/lava ====
    private int getSurfaceY(BlockPos pos) {
        ChunkTerrainData data = analyzeChunk(pos);
        Integer y = data.heightMap.get(new BlockPos(pos.getX(), 0, pos.getZ()));
        return y == null ? -1 : y;
    }

    // Verifica se uma amostra pertence ao chão (próxima da superfície) e segura
    private boolean isSegmentSampleGrounded(BlockPos pos) {
        int surfaceY = getSurfaceY(pos);
        if (surfaceY == -1) return false;
        int expectedGroundY = surfaceY + 1;
        if (Math.abs(pos.getY() - expectedGroundY) > 1) return false;
        // evita proximidade de perigos do chunk
        ChunkTerrainData data = analyzeChunk(pos);
        for (BlockPos hazard : data.hazardPositions) {
            if (hazard.getX() == pos.getX() && hazard.getZ() == pos.getZ()) {
                return false; // coluna contém perigo (água/lava/fogo)
            }
            if (getDistance(pos, hazard) <= 2.0) {
                return false; // muito perto de perigo
            }
        }
        return true;
    }
}