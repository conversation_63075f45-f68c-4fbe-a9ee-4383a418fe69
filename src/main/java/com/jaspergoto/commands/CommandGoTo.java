package com.jaspergoto.commands;

import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.BlockPos;
import net.minecraft.util.ChatComponentText;
import net.minecraft.util.EnumChatFormatting;

import com.jaspergoto.JasperGoTo;

import java.util.List;

public class CommandGoTo extends CommandBase {
    
    @Override
    public String getCommandName() {
        return "jaspergoto";
    }
    
    @Override
    public String getCommandUsage(ICommandSender sender) {
        return "/jaspergoto <x> <y> <z> - Vai automaticamente até as coordenadas\n" +
               "/jaspergoto stop - Para o movimento\n" +
               "/jaspergoto clear - Limpa o caminho";
    }
    
    @Override
    public void processCommand(ICommandSender sender, String[] args) throws CommandException {
        if (!(sender instanceof EntityPlayer)) {
            sender.addChatMessage(new ChatComponentText("Este comando só pode ser usado por jogadores!"));
            return;
        }
        
        EntityPlayer player = (EntityPlayer) sender;
        
        if (args.length == 0) {
            showHelp(player);
            return;
        }
        
        // Comando stop
        if (args[0].equalsIgnoreCase("stop")) {
            JasperGoTo.stopPathfinding();
            player.addChatMessage(new ChatComponentText(
                EnumChatFormatting.YELLOW + "[JasperGoTo] " + 
                EnumChatFormatting.RED + "Movimento parado!"
            ));
            return;
        }
        
        // Comando clear
        if (args[0].equalsIgnoreCase("clear")) {
            JasperGoTo.clearPath();
            player.addChatMessage(new ChatComponentText(
                EnumChatFormatting.YELLOW + "[JasperGoTo] " + 
                EnumChatFormatting.WHITE + "Caminho limpo!"
            ));
            return;
        }
        
        // Comando de coordenadas
        if (args.length < 3) {
            player.addChatMessage(new ChatComponentText(
                EnumChatFormatting.RED + "Uso: /jaspergoto <x> <y> <z>"
            ));
            return;
        }
        
        try {
            int x = parseInt(args[0]);
            int y = parseInt(args[1]);
            int z = parseInt(args[2]);
            
            // Validar coordenadas
            if (y < 0 || y > 255) {
                player.addChatMessage(new ChatComponentText(
                    EnumChatFormatting.RED + "Y deve estar entre 0 e 255!"
                ));
                return;
            }
            
            BlockPos target = new BlockPos(x, y, z);
            BlockPos playerPos = new BlockPos(player.posX, player.posY, player.posZ);
            
            // Calcular distância
            double distance = Math.sqrt(
                Math.pow(x - player.posX, 2) + 
                Math.pow(y - player.posY, 2) + 
                Math.pow(z - player.posZ, 2)
            );
            
            // Mensagem de início
            player.addChatMessage(new ChatComponentText(
                EnumChatFormatting.YELLOW + "[JasperGoTo] " + 
                EnumChatFormatting.GREEN + "Calculando rota para " + 
                EnumChatFormatting.WHITE + x + ", " + y + ", " + z + 
                EnumChatFormatting.GRAY + " (distância: " + String.format("%.1f", distance) + " blocos)"
            ));
            
            // Iniciar pathfinding
            boolean success = JasperGoTo.startPathfinding(playerPos, target);
            
            if (success) {
                player.addChatMessage(new ChatComponentText(
                    EnumChatFormatting.YELLOW + "[JasperGoTo] " + 
                    EnumChatFormatting.GREEN + "✓ Rota encontrada! Iniciando movimento..."
                ));
            } else {
                player.addChatMessage(new ChatComponentText(
                    EnumChatFormatting.YELLOW + "[JasperGoTo] " + 
                    EnumChatFormatting.RED + "✗ Não foi possível encontrar um caminho!"
                ));
            }
            
        } catch (NumberFormatException e) {
            player.addChatMessage(new ChatComponentText(
                EnumChatFormatting.RED + "Coordenadas inválidas! Use números inteiros."
            ));
        }
    }
    
    private void showHelp(EntityPlayer player) {
        player.addChatMessage(new ChatComponentText(
            EnumChatFormatting.GOLD + "=== JasperGoTo - Comandos ==="
        ));
        player.addChatMessage(new ChatComponentText(
            EnumChatFormatting.YELLOW + "/jaspergoto <x> <y> <z> " + 
            EnumChatFormatting.WHITE + "- Vai até as coordenadas"
        ));
        player.addChatMessage(new ChatComponentText(
            EnumChatFormatting.YELLOW + "/jaspergoto stop " + 
            EnumChatFormatting.WHITE + "- Para o movimento"
        ));
        player.addChatMessage(new ChatComponentText(
            EnumChatFormatting.YELLOW + "/jaspergoto clear " + 
            EnumChatFormatting.WHITE + "- Limpa o caminho"
        ));
    }
    
    @Override
    public int getRequiredPermissionLevel() {
        return 0; // Qualquer jogador pode usar
    }
    
    @Override
    public List<String> addTabCompletionOptions(ICommandSender sender, String[] args, BlockPos pos) {
        if (args.length == 1) {
            return getListOfStringsMatchingLastWord(args, "stop", "clear");
        }
        return null;
    }
}
