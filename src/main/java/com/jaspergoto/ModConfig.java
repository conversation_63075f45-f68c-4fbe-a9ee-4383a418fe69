package com.jaspergoto;

import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

@SideOnly(Side.CLIENT)
public class ModConfig {
    
    // Configurações de movimento
    public static boolean autoWalkEnabled = false;
    public static boolean autoSprintEnabled = false;
    public static boolean autoJumpEnabled = false;
    public static boolean autoSneak = false;
    
    // Configurações de HUD
    public static boolean showHUD = true;
    public static int hudX = 5;
    public static int hudY = 5;
    public static int hudColor = 0xFFFFFF;
    
    // Configurações avançadas
    public static boolean stopOnDamage = true;
    public static boolean stopInWater = false;
    public static boolean stopOnLowFood = true;
    public static int minFoodLevel = 6;
    
    // Debug
    public static boolean debugMode = false;
    
    public static void toggleAutoWalk() {
        autoWalkEnabled = !autoWalkEnabled;
    }
    
    public static void toggleAutoSprint() {
        autoSprintEnabled = !autoSprintEnabled;
    }
    
    public static void toggleAutoJump() {
        autoJumpEnabled = !autoJumpEnabled;
    }
    
    public static void toggleAutoSneak() {
        autoSneak = !autoSneak;
    }
    
    public static void resetAll() {
        autoWalkEnabled = false;
        autoSprintEnabled = false;
        autoJumpEnabled = false;
        autoSneak = false;
    }
}
