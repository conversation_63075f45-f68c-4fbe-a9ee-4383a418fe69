import java.util.*;

/**
 * Detects and classifies obstacles in the 3D environment.
 * Identifies blocks, liquids, falls, and other hazards that affect pathfinding.
 */
public class ObstacleDetector {
    
    // World representation interface
    public interface WorldAccessor {
        BlockType getBlockAt(int x, int y, int z);
        boolean isLoaded(int x, int y, int z);
    }
    
    // Block types that can exist in the world
    public enum BlockType {
        AIR(false, false, false),
        STONE(true, false, false),
        DIRT(true, false, false),
        GRASS(true, false, false),
        WATER(false, true, false),
        LAVA(false, true, true),
        SAND(true, false, false),
        GRAVEL(true, false, false),
        WOOD(true, false, false),
        LEAVES(true, false, false),
        GLASS(true, false, false),
        FENCE(true, false, false),
        WALL(true, false, false),
        SOUL_SAND(true, false, false),
        ICE(true, false, false),
        PACKED_ICE(true, false, false),
        LADDER(false, false, false),
        VINE(false, false, false),
        COBWEB(false, false, false),
        CACTUS(true, false, true),
        FIRE(false, false, true);
        
        private final boolean solid;
        private final boolean liquid;
        private final boolean hazardous;
        
        BlockType(boolean solid, boolean liquid, boolean hazardous) {
            this.solid = solid;
            this.liquid = liquid;
            this.hazardous = hazardous;
        }
        
        public boolean isSolid() { return solid; }
        public boolean isLiquid() { return liquid; }
        public boolean isHazardous() { return hazardous; }
        public boolean isClimbable() { 
            return this == LADDER || this == VINE;
        }
    }
    
    // Obstacle classification
    public enum ObstacleType {
        NONE,
        SOLID_BLOCK,
        LIQUID,
        FALL_HAZARD,
        LAVA_HAZARD,
        FIRE_HAZARD,
        CACTUS_HAZARD,
        PARTIAL_BLOCK,
        CLIMBABLE
    }
    
    // Obstacle information
    public static class Obstacle {
        private final ObstacleType type;
        private final int x, y, z;
        private final double dangerLevel;
        private final boolean passable;
        
        public Obstacle(ObstacleType type, int x, int y, int z, 
                       double dangerLevel, boolean passable) {
            this.type = type;
            this.x = x;
            this.y = y;
            this.z = z;
            this.dangerLevel = dangerLevel;
            this.passable = passable;
        }
        
        // Getters
        public ObstacleType getType() { return type; }
        public int getX() { return x; }
        public int getY() { return y; }
        public int getZ() { return z; }
        public double getDangerLevel() { return dangerLevel; }
        public boolean isPassable() { return passable; }
        
        @Override
        public String toString() {
            return String.format("Obstacle[type=%s, pos=(%d,%d,%d), danger=%.1f, passable=%s]",
                    type, x, y, z, dangerLevel, passable);
        }
    }
    
    private final WorldAccessor world;
    private final int maxFallDistance;
    private final boolean avoidWater;
    private final boolean avoidLava;
    
    /**
     * Creates an obstacle detector with specified parameters.
     */
    public ObstacleDetector(WorldAccessor world, int maxFallDistance, 
                           boolean avoidWater, boolean avoidLava) {
        this.world = world;
        this.maxFallDistance = maxFallDistance;
        this.avoidWater = avoidWater;
        this.avoidLava = avoidLava;
    }
    
    /**
     * Creates an obstacle detector with default parameters.
     */
    public ObstacleDetector(WorldAccessor world) {
        this(world, 3, false, true);
    }
    
    /**
     * Detects obstacles at a specific position.
     */
    public Obstacle detectObstacle(int x, int y, int z) {
        if (!world.isLoaded(x, y, z)) {
            return new Obstacle(ObstacleType.SOLID_BLOCK, x, y, z, 0, false);
        }
        
        BlockType block = world.getBlockAt(x, y, z);
        
        // Check for solid blocks
        if (block.isSolid()) {
            if (block.isHazardous()) {
                return new Obstacle(ObstacleType.CACTUS_HAZARD, x, y, z, 10, false);
            }
            return new Obstacle(ObstacleType.SOLID_BLOCK, x, y, z, 0, false);
        }
        
        // Check for liquids
        if (block.isLiquid()) {
            if (block == BlockType.LAVA) {
                return new Obstacle(ObstacleType.LAVA_HAZARD, x, y, z, 
                                   avoidLava ? 100 : 5, !avoidLava);
            } else if (block == BlockType.WATER) {
                return new Obstacle(ObstacleType.LIQUID, x, y, z, 
                                   avoidWater ? 10 : 1, !avoidWater);
            }
        }
        
        // Check for hazards
        if (block == BlockType.FIRE) {
            return new Obstacle(ObstacleType.FIRE_HAZARD, x, y, z, 8, false);
        }
        
        // Check for climbable blocks
        if (block.isClimbable()) {
            return new Obstacle(ObstacleType.CLIMBABLE, x, y, z, 0, true);
        }
        
        // Check for fall hazards
        if (checkFallHazard(x, y, z)) {
            int fallDistance = calculateFallDistance(x, y, z);
            double dangerLevel = Math.min(fallDistance * 2.0, 20.0);
            return new Obstacle(ObstacleType.FALL_HAZARD, x, y, z, 
                              dangerLevel, fallDistance <= maxFallDistance);
        }
        
        return new Obstacle(ObstacleType.NONE, x, y, z, 0, true);
    }
    
    /**
     * Checks if a position is walkable (has solid ground and space above).
     */
    public boolean isWalkable(int x, int y, int z) {
        // Check if there's solid ground below
        BlockType groundBlock = world.getBlockAt(x, y - 1, z);
        if (!groundBlock.isSolid() && !groundBlock.isLiquid() && 
            !groundBlock.isClimbable()) {
            return false;
        }
        
        // Check if the current position is not solid
        BlockType currentBlock = world.getBlockAt(x, y, z);
        if (currentBlock.isSolid()) {
            return false;
        }
        
        // Check if there's enough headroom
        BlockType aboveBlock = world.getBlockAt(x, y + 1, z);
        if (aboveBlock.isSolid()) {
            return false;
        }
        
        // Check for hazards
        if (currentBlock.isHazardous() || aboveBlock.isHazardous()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Checks if there's a fall hazard at the position.
     */
    private boolean checkFallHazard(int x, int y, int z) {
        // Check if there's no solid ground below
        for (int dy = 1; dy <= 3; dy++) {
            BlockType below = world.getBlockAt(x, y - dy, z);
            if (below.isSolid() || below.isLiquid()) {
                return false; // Found ground within safe distance
            }
        }
        return true;
    }
    
    /**
     * Calculates the fall distance from a position.
     */
    private int calculateFallDistance(int x, int y, int z) {
        int fallDistance = 0;
        for (int dy = 1; dy <= 256; dy++) {
            if (y - dy < 0) break;
            
            BlockType below = world.getBlockAt(x, y - dy, z);
            if (below.isSolid() || below.isLiquid()) {
                break;
            }
            fallDistance++;
        }
        return fallDistance;
    }
    
    /**
     * Scans a radius around a position for obstacles.
     */
    public List<Obstacle> scanArea(int centerX, int centerY, int centerZ, int radius) {
        List<Obstacle> obstacles = new ArrayList<>();
        
        for (int x = centerX - radius; x <= centerX + radius; x++) {
            for (int y = centerY - radius; y <= centerY + radius; y++) {
                for (int z = centerZ - radius; z <= centerZ + radius; z++) {
                    Obstacle obstacle = detectObstacle(x, y, z);
                    if (obstacle.getType() != ObstacleType.NONE) {
                        obstacles.add(obstacle);
                    }
                }
            }
        }
        
        return obstacles;
    }
    
    /**
     * Checks if a path between two adjacent nodes is clear.
     */
    public boolean isPathClear(Node from, Node to) {
        // Check if nodes are adjacent
        if (!from.isAdjacentTo(to)) {
            return false;
        }
        
        int x1 = from.getX(), y1 = from.getY(), z1 = from.getZ();
        int x2 = to.getX(), y2 = to.getY(), z2 = to.getZ();
        
        // Check vertical movement
        if (y2 > y1) {
            // Moving up - check for ceiling
            for (int y = y1 + 1; y <= y2 + 1; y++) {
                if (world.getBlockAt(x2, y, z2).isSolid()) {
                    return false;
                }
            }
        } else if (y2 < y1) {
            // Moving down - check fall distance
            if (y1 - y2 > maxFallDistance) {
                int fallDist = calculateFallDistance(x2, y2, z2);
                if (fallDist > maxFallDistance) {
                    return false;
                }
            }
        }
        
        // Check diagonal movement for corner cutting
        if (Math.abs(x2 - x1) + Math.abs(z2 - z1) == 2) {
            // Diagonal movement - check corners
            if (world.getBlockAt(x1, y1, z2).isSolid() ||
                world.getBlockAt(x2, y1, z1).isSolid()) {
                return false; // Corner is blocked
            }
            if (world.getBlockAt(x1, y1 + 1, z2).isSolid() ||
                world.getBlockAt(x2, y1 + 1, z1).isSolid()) {
                return false; // Upper corner is blocked
            }
        }
        
        return isWalkable(x2, y2, z2);
    }
    
    /**
     * Gets movement penalty for a specific position based on obstacles.
     */
    public double getMovementPenalty(int x, int y, int z) {
        Obstacle obstacle = detectObstacle(x, y, z);
        
        switch (obstacle.getType()) {
            case NONE:
                return 1.0;
            case LIQUID:
                return avoidWater ? 100.0 : 3.0;
            case LAVA_HAZARD:
                return avoidLava ? 1000.0 : 10.0;
            case FALL_HAZARD:
                return 1.0 + obstacle.getDangerLevel() * 0.5;
            case FIRE_HAZARD:
            case CACTUS_HAZARD:
                return 50.0;
            case CLIMBABLE:
                return 1.5;
            default:
                return Double.MAX_VALUE;
        }
    }
    
    /**
     * Checks if a position requires special movement (jumping, swimming, etc).
     */
    public MovementType getRequiredMovement(Node from, Node to) {
        int dy = to.getY() - from.getY();
        
        // Check for jumping
        if (dy == 1) {
            BlockType blockAtTo = world.getBlockAt(to.getX(), to.getY() - 1, to.getZ());
            if (blockAtTo.isSolid()) {
                return MovementType.JUMP;
            }
        }
        
        // Check for swimming
        BlockType blockAtTo = world.getBlockAt(to.getX(), to.getY(), to.getZ());
        if (blockAtTo.isLiquid()) {
            return MovementType.SWIM;
        }
        
        // Check for climbing
        if (blockAtTo.isClimbable()) {
            return MovementType.CLIMB;
        }
        
        // Check for falling
        if (dy < 0) {
            return MovementType.FALL;
        }
        
        return MovementType.WALK;
    }
    
    public enum MovementType {
        WALK,
        JUMP,
        FALL,
        SWIM,
        CLIMB
    }
}
