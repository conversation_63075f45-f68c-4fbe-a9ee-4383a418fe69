// Versão simplificada do JasperGoTo para compilação
public class JasperGoToSimples {
    public static final String MODID = "jaspergoto";
    public static final String VERSION = "1.0.0";
    public static final String NAME = "JasperGoTo - Pathfinding";
    
    public static void main(String[] args) {
        System.out.println("JasperGoTo Mod - Versão " + VERSION);
        System.out.println("Mod ID: " + MODID);
        System.out.println("Funcionalidades:");
        System.out.println("- Comando /jaspergoto x y z");
        System.out.println("- Pathfinding automático");
        System.out.println("- Visualização do trajeto");
        System.out.println("- Sistema anti-travamento");
    }
}