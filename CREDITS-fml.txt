This is Forge Mod Loader.

You can find the source code at all times at https://github.com/MinecraftForge/FML

This minecraft mod is a clean open source implementation of a mod loader for minecraft servers
and minecraft clients.

The code is authored by cpw.

It began by partially implementing an API defined by the client side ModLoader, authored by <PERSON><PERSON><PERSON><PERSON>.
http://www.minecraftforum.net/topic/75440-
This support has been dropped as of Minecraft release 1.7, as <PERSON><PERSON><PERSON><PERSON> no longer maintains ModLoader.

It also contains suggestions and hints and generous helpings of code from <PERSON><PERSON><PERSON><PERSON>, author of MinecraftForge.
http://www.minecraftforge.net/

Additionally, it contains an implementation of topological sort based on that 
published at http://keithschwarz.com/interesting/code/?dir=topological-sort

It also contains code from the Maven project for performing versioned dependency
resolution. http://maven.apache.org/

It also contains a partial repackaging of the javaxdelta library from http://sourceforge.net/projects/javaxdelta/
with credit to it's authors.

Forge Mod Loader downloads components from the Minecraft Coder Pack
(http://mcp.ocean-labs.de/index.php/Main_Page) with kind permission from the MCP team.

