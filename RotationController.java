import java.util.concurrent.atomic.AtomicBoolean;
import java.util.logging.Logger;

/**
 * Controls smooth camera and player rotation for natural movement.
 * Implements interpolation and easing functions for realistic rotation behavior.
 */
public class RotationController {
    private static final Logger LOGGER = Logger.getLogger(RotationController.class.getName());
    
    // Rotation configuration
    private static final float MAX_ROTATION_SPEED = 180.0f; // degrees per second
    private static final float SMOOTH_FACTOR = 0.15f; // smoothing interpolation factor
    private static final float ROTATION_THRESHOLD = 0.5f; // minimum rotation difference
    private static final long UPDATE_INTERVAL = 20; // milliseconds
    
    // Current rotation state
    private volatile float currentYaw;
    private volatile float currentPitch;
    private volatile float targetYaw;
    private volatile float targetPitch;
    
    // Rotation velocity for smooth transitions
    private float yawVelocity;
    private float pitchVelocity;
    
    // Control flags
    private final AtomicBoolean isRotating;
    private final AtomicBoolean smoothingEnabled;
    private Thread rotationThread;
    
    // Rotation limits
    private static final float MIN_PITCH = -90.0f;
    private static final float MAX_PITCH = 90.0f;
    
    /**
     * Rotation data structure
     */
    public static class Rotation {
        public final float yaw;
        public final float pitch;
        
        public Rotation(float yaw, float pitch) {
            this.yaw = normalizeYaw(yaw);
            this.pitch = clampPitch(pitch);
        }
        
        @Override
        public String toString() {
            return String.format("Rotation[yaw=%.2f, pitch=%.2f]", yaw, pitch);
        }
    }
    
    /**
     * Constructor initializes rotation controller
     */
    public RotationController() {
        this.currentYaw = 0.0f;
        this.currentPitch = 0.0f;
        this.targetYaw = 0.0f;
        this.targetPitch = 0.0f;
        this.yawVelocity = 0.0f;
        this.pitchVelocity = 0.0f;
        this.isRotating = new AtomicBoolean(false);
        this.smoothingEnabled = new AtomicBoolean(true);
    }
    
    /**
     * Start the rotation update thread
     */
    public void start() {
        if (!isRotating.compareAndSet(false, true)) {
            LOGGER.warning("Rotation controller already running");
            return;
        }
        
        rotationThread = new Thread(this::rotationUpdateLoop, "RotationController");
        rotationThread.setDaemon(true);
        rotationThread.start();
        LOGGER.info("Rotation controller started");
    }
    
    /**
     * Stop the rotation update thread
     */
    public void stop() {
        if (!isRotating.compareAndSet(true, false)) {
            return;
        }
        
        if (rotationThread != null) {
            try {
                rotationThread.interrupt();
                rotationThread.join(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        LOGGER.info("Rotation controller stopped");
    }
    
    /**
     * Main rotation update loop
     */
    private void rotationUpdateLoop() {
        while (isRotating.get()) {
            try {
                updateRotation();
                Thread.sleep(UPDATE_INTERVAL);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                LOGGER.severe("Error in rotation update: " + e.getMessage());
            }
        }
    }
    
    /**
     * Update rotation towards target with smoothing
     */
    private void updateRotation() {
        if (!needsRotationUpdate()) {
            return;
        }
        
        float deltaTime = UPDATE_INTERVAL / 1000.0f;
        
        // Update yaw
        if (Math.abs(targetYaw - currentYaw) > ROTATION_THRESHOLD) {
            if (smoothingEnabled.get()) {
                currentYaw = smoothRotation(currentYaw, targetYaw, yawVelocity, deltaTime, true);
            } else {
                currentYaw = directRotation(currentYaw, targetYaw, deltaTime, true);
            }
        }
        
        // Update pitch
        if (Math.abs(targetPitch - currentPitch) > ROTATION_THRESHOLD) {
            if (smoothingEnabled.get()) {
                currentPitch = smoothRotation(currentPitch, targetPitch, pitchVelocity, deltaTime, false);
            } else {
                currentPitch = directRotation(currentPitch, targetPitch, deltaTime, false);
            }
        }
        
        // Apply the rotation (would integrate with game client here)
        applyRotation(currentYaw, currentPitch);
    }
    
    /**
     * Check if rotation update is needed
     */
    private boolean needsRotationUpdate() {
        float yawDiff = Math.abs(targetYaw - currentYaw);
        float pitchDiff = Math.abs(targetPitch - currentPitch);
        return yawDiff > ROTATION_THRESHOLD || pitchDiff > ROTATION_THRESHOLD;
    }
    
    /**
     * Smooth rotation using interpolation and easing
     */
    private float smoothRotation(float current, float target, float velocity, float deltaTime, boolean isYaw) {
        float difference = target - current;
        
        // Handle yaw wrapping (shortest path around circle)
        if (isYaw) {
            difference = normalizeAngleDifference(difference);
        }
        
        // Apply easing function (smooth deceleration)
        float easedDifference = easeInOutCubic(Math.min(Math.abs(difference) / 180.0f, 1.0f));
        float direction = Math.signum(difference);
        
        // Calculate smooth velocity
        float targetVelocity = direction * easedDifference * MAX_ROTATION_SPEED;
        velocity = lerp(velocity, targetVelocity, SMOOTH_FACTOR);
        
        // Apply velocity with delta time
        float newRotation = current + velocity * deltaTime;
        
        // Update velocity for next frame
        if (isYaw) {
            yawVelocity = velocity;
        } else {
            pitchVelocity = velocity;
        }
        
        // Prevent overshooting
        if (Math.abs(target - newRotation) < Math.abs(velocity * deltaTime)) {
            return target;
        }
        
        return isYaw ? normalizeYaw(newRotation) : clampPitch(newRotation);
    }
    
    /**
     * Direct rotation without smoothing
     */
    private float directRotation(float current, float target, float deltaTime, boolean isYaw) {
        float difference = target - current;
        
        if (isYaw) {
            difference = normalizeAngleDifference(difference);
        }
        
        float maxStep = MAX_ROTATION_SPEED * deltaTime;
        float step = Math.min(Math.abs(difference), maxStep) * Math.signum(difference);
        
        float newRotation = current + step;
        
        return isYaw ? normalizeYaw(newRotation) : clampPitch(newRotation);
    }
    
    /**
     * Apply rotation to game (placeholder for actual implementation)
     */
    private void applyRotation(float yaw, float pitch) {
        // This would integrate with the game client to actually apply the rotation
        // For now, just log significant changes
        if (Math.abs(yaw - currentYaw) > 5.0f || Math.abs(pitch - currentPitch) > 5.0f) {
            LOGGER.fine(String.format("Applied rotation: yaw=%.2f, pitch=%.2f", yaw, pitch));
        }
    }
    
    /**
     * Set target rotation with smooth transition
     */
    public void smoothRotateTo(float targetYaw, float targetPitch) {
        this.targetYaw = normalizeYaw(targetYaw);
        this.targetPitch = clampPitch(targetPitch);
        LOGGER.fine(String.format("Smooth rotate to: yaw=%.2f, pitch=%.2f", this.targetYaw, this.targetPitch));
    }
    
    /**
     * Set rotation instantly without smoothing
     */
    public void setRotationInstant(float yaw, float pitch) {
        this.currentYaw = normalizeYaw(yaw);
        this.currentPitch = clampPitch(pitch);
        this.targetYaw = this.currentYaw;
        this.targetPitch = this.currentPitch;
        this.yawVelocity = 0.0f;
        this.pitchVelocity = 0.0f;
        LOGGER.info(String.format("Instant rotation set: yaw=%.2f, pitch=%.2f", currentYaw, currentPitch));
    }
    
    /**
     * Look at a specific position in 3D space
     */
    public void lookAt(double x, double y, double z, double fromX, double fromY, double fromZ) {
        double dx = x - fromX;
        double dy = y - fromY;
        double dz = z - fromZ;
        
        // Calculate yaw (horizontal rotation)
        float targetYaw = (float) Math.toDegrees(Math.atan2(-dx, dz));
        
        // Calculate pitch (vertical rotation)
        double horizontalDistance = Math.sqrt(dx * dx + dz * dz);
        float targetPitch = (float) Math.toDegrees(Math.atan2(-dy, horizontalDistance));
        
        smoothRotateTo(targetYaw, targetPitch);
        LOGGER.info(String.format("Looking at position: (%.2f, %.2f, %.2f)", x, y, z));
    }
    
    /**
     * Rotate by relative amounts
     */
    public void rotateBy(float deltaYaw, float deltaPitch) {
        float newYaw = currentYaw + deltaYaw;
        float newPitch = currentPitch + deltaPitch;
        smoothRotateTo(newYaw, newPitch);
    }
    
    /**
     * Get current rotation
     */
    public Rotation getCurrentRotation() {
        return new Rotation(currentYaw, currentPitch);
    }
    
    /**
     * Get target rotation
     */
    public Rotation getTargetRotation() {
        return new Rotation(targetYaw, targetPitch);
    }
    
    /**
     * Check if currently rotating
     */
    public boolean isRotating() {
        return needsRotationUpdate();
    }
    
    /**
     * Enable or disable smoothing
     */
    public void setSmoothingEnabled(boolean enabled) {
        smoothingEnabled.set(enabled);
        LOGGER.info("Smoothing " + (enabled ? "enabled" : "disabled"));
    }
    
    /**
     * Reset rotation to default
     */
    public void reset() {
        setRotationInstant(0.0f, 0.0f);
    }
    
    // Utility methods
    
    /**
     * Normalize yaw to -180 to 180 range
     */
    private static float normalizeYaw(float yaw) {
        while (yaw > 180.0f) yaw -= 360.0f;
        while (yaw < -180.0f) yaw += 360.0f;
        return yaw;
    }
    
    /**
     * Clamp pitch to valid range
     */
    private static float clampPitch(float pitch) {
        return Math.max(MIN_PITCH, Math.min(MAX_PITCH, pitch));
    }
    
    /**
     * Normalize angle difference for shortest path
     */
    private static float normalizeAngleDifference(float difference) {
        while (difference > 180.0f) difference -= 360.0f;
        while (difference < -180.0f) difference += 360.0f;
        return difference;
    }
    
    /**
     * Linear interpolation
     */
    private static float lerp(float a, float b, float t) {
        return a + (b - a) * t;
    }
    
    /**
     * Ease in-out cubic function for smooth acceleration/deceleration
     */
    private static float easeInOutCubic(float t) {
        if (t < 0.5f) {
            return 4.0f * t * t * t;
        } else {
            float p = 2.0f * t - 2.0f;
            return 1.0f + p * p * p / 2.0f;
        }
    }
    
    /**
     * Calculate rotation speed based on distance
     */
    public float calculateRotationSpeed(float angleDifference) {
        // Slower rotation for small adjustments, faster for large turns
        float normalizedDiff = Math.min(Math.abs(angleDifference) / 180.0f, 1.0f);
        return MAX_ROTATION_SPEED * easeInOutCubic(normalizedDiff);
    }
}
